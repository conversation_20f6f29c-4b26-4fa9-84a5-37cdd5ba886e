# Story 2.4: Mesaj Geçmişini Görüntüleme API'si

## Status
Draft

## Story
**As a** kullanıc<PERSON>,
**I want** belirli bir konuşmadaki geçmiş mesajların tümünü görüntülemek,
**so that** sohbetin içeriğini takip edebilirim.

## Acceptance Criteria
1. Bel<PERSON>li bir konuşma ID'sine ait tüm mesajları getiren bir `GET /api/messages/:conversationId` endpoint'i oluşturulmalıdır. [cite: Epic 2.4]
2. Bu endpoint, sadece konuşmaya dahil olan kullanıcılar tarafından erişilebilir olmalıdır. [cite: Epic 2.4]
3. Performans için yanıtta sayfalama (pagination) desteklenmelidir. [cite: Epic 2.4]

## Tasks / Subtasks
- [ ] Task 1: Message Data Model (AC: 1)
  - [ ] Message schema oluştur
  - [ ] ConversationId ve senderId referansları
  - [ ] Content, isRead, timestamps alanları
  - [ ] Model validations ve indexes
- [ ] Task 2: Message Service (AC: 1, 2, 3)
  - [ ] Get messages by conversation logic
  - [ ] Pagination implementation
  - [ ] Access control validation
  - [ ] Message sorting (newest first)
- [ ] Task 3: Message API Endpoints (AC: 1, 2, 3)
  - [ ] GET /api/messages/:conversationId endpoint
  - [ ] Query parameters (page, limit)
  - [ ] Request validation middleware
  - [ ] Response formatting with pagination
- [ ] Task 4: Message Controller (AC: 1, 2, 3)
  - [ ] Get messages controller
  - [ ] Pagination logic
  - [ ] Error handling
  - [ ] Authentication integration
- [ ] Task 5: Unit ve Integration Testleri (AC: 1-3)
  - [ ] Message model testleri
  - [ ] Message service testleri
  - [ ] API endpoint testleri
  - [ ] Pagination testleri
  - [ ] Access control testleri

## Dev Notes

### Previous Story Insights
Story 2.1, 2.2 ve 2.3'ten öğrenilenler:
- Socket.IO server kurulu ve Redis entegrasyonu mevcut
- JWT authentication middleware çalışıyor
- User model ve authentication sistemi mevcut
- Conversation model ve API'leri implement edilmiş
- Real-time user status tracking çalışıyor

### Technology Stack
**Database Requirements** [Source: Epic 2.4]:
- **Primary**: MongoDB with Mongoose ODM
- **Schema**: Message model with conversation and user references
- **Relationships**: ConversationId -> Conversation, SenderId -> User
- **Indexing**: Efficient queries for conversation messages with pagination

### API Specifications
**GET /api/messages/:conversationId**:
```javascript
// Request
GET /api/messages/conv123?page=1&limit=20

// Response
{
  "success": true,
  "message": "Messages retrieved successfully",
  "data": {
    "messages": [
      {
        "_id": "msg123",
        "conversationId": "conv123",
        "senderId": {
          "_id": "user123",
          "username": "john_doe"
        },
        "content": "Hello there!",
        "isRead": true,
        "createdAt": "2025-01-04T10:35:00.000Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 45,
      "totalPages": 3,
      "hasNext": true,
      "hasPrev": false
    }
  }
}
```

### Security Considerations
**Authentication & Authorization**:
- JWT token required for endpoint
- Users can only access messages from conversations they participate in
- Conversation access validation before message retrieval
- Input sanitization and validation

### Database Schema Design
**Message Model**:
```javascript
{
  _id: ObjectId,
  conversationId: ObjectId, // Reference to Conversation
  senderId: ObjectId, // Reference to User
  content: String, // Message text content
  isRead: Boolean, // Read status (default: false)
  createdAt: Date,
  updatedAt: Date
}
```

**Indexes**:
- `conversationId` index for efficient message queries
- Compound index on `conversationId + createdAt` for pagination
- `senderId` index for sender-based queries

### File Locations
**Based on Project Structure** [Source: architecture/project-structure.md]:
- Model: `src/models/message.model.ts`
- Service: `src/services/message.service.ts`
- Controller: `src/api/controllers/message.controller.ts`
- Routes: `src/api/routes/message.routes.ts`
- Validators: `src/api/validators/message.validator.ts`
- Types: `src/types/message.types.ts`

### Integration Points
**Existing Systems**:
- Conversation model for access validation
- User model for sender information
- JWT authentication middleware
- Error handling with AppError
- Logger for message events
- MongoDB with Mongoose ODM

### Business Logic
**Message Retrieval**:
1. Validate conversation ID format
2. Check if conversation exists
3. Verify user is participant in conversation
4. Retrieve messages with pagination
5. Populate sender information
6. Sort by creation date (newest first)
7. Return paginated response

### Pagination Implementation
**Query Parameters**:
- `page`: Page number (default: 1)
- `limit`: Messages per page (default: 20, max: 100)

**Response Format**:
- Include pagination metadata
- Calculate total pages and navigation flags
- Efficient database queries with skip/limit

## Testing

### Testing Standards
**Test File Locations** [Source: architecture/coding-standarts.md]:
- Unit tests: Same folder as source files with `.spec.ts` extension
- Integration tests: `/tests` directory in project root

**Testing Framework** [Source: architecture/tech-stack.md]:
- **Primary**: Jest 29.7.0
- **Database Testing**: MongoDB Memory Server for testing

**Specific Testing Requirements for This Story**:
- Test message model validation and relationships
- Test message service pagination logic
- Test API endpoints with authentication
- Test access control (only participants can access)
- Test pagination edge cases
- Test error handling scenarios

## Change Log
| Date | Version | Description | Author |
|------|---------|-------------|--------|
| 2025-01-04 | 1.0 | Initial story creation | Scrum Master (Bob) |

## Dev Agent Record

### Agent Model Used
*To be filled by dev agent*

### Debug Log References
*To be filled by dev agent*

### Completion Notes List
*To be filled by dev agent*

### File List
*To be filled by dev agent*

## QA Results
*Results from QA Agent review will be added here*
