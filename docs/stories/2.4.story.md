# Story 2.4: Mesaj Geçmişini Görüntüleme API'si

## Status

Approved

## Story

**As a** kullanıc<PERSON>,
**I want** belirli bir konuşmadaki geçmiş mesajların tümünü görüntülemek,
**so that** sohbetin içeriğini takip edebilirim.

## Acceptance Criteria

1. Bel<PERSON>li bir konuşma ID'sine ait tüm mesajları getiren bir `GET /api/messages/:conversationId` endpoint'i oluşturulmalıdır. [cite: Epic 2.4]
2. Bu endpoint, sadece konuşmaya dahil olan kullanıcılar tarafından erişilebilir olmalıdır. [cite: Epic 2.4]
3. Performans için yanıtta sayfalama (pagination) desteklenmelidir. [cite: Epic 2.4]

## Tasks / Subtasks

- [ ] Task 1: Message Data Model (AC: 1)
  - [ ] Message schema oluştur
  - [ ] ConversationId ve senderId referansları
  - [ ] Content, isRead, timestamps alanları
  - [ ] Model validations ve indexes
- [ ] Task 2: Message Service (AC: 1, 2, 3)
  - [ ] Get messages by conversation logic
  - [ ] Pagination implementation
  - [ ] Access control validation
  - [ ] Message sorting (newest first)
- [ ] Task 3: Message API Endpoints (AC: 1, 2, 3)
  - [ ] GET /api/messages/:conversationId endpoint
  - [ ] Query parameters (page, limit)
  - [ ] Request validation middleware
  - [ ] Response formatting with pagination
- [ ] Task 4: Message Controller (AC: 1, 2, 3)
  - [ ] Get messages controller
  - [ ] Pagination logic
  - [ ] Error handling
  - [ ] Authentication integration
- [ ] Task 5: Unit ve Integration Testleri (AC: 1-3)
  - [ ] Message model testleri
  - [ ] Message service testleri
  - [ ] API endpoint testleri
  - [ ] Pagination testleri
  - [ ] Access control testleri

## Dev Notes

### Previous Story Insights

**Story 2.1, 2.2 ve 2.3'ten öğrenilenler:**

- Socket.IO server kurulu ve Redis entegrasyonu mevcut
- JWT authentication middleware çalışıyor ve güvenilir
- User model ve authentication sistemi mevcut ve stabil
- Conversation model ve API'leri implement edilmiş (POST /api/conversations, GET /api/conversations)
- Real-time user status tracking çalışıyor
- Message model zaten mevcut ve çalışıyor [Source: src/models/message.model.ts]
- Pagination pattern'leri önceki story'lerde kullanılmış
- Error handling AppError ile standardize edilmiş

### Technology Stack

**Database Requirements** [Source: architecture/tech-stack.md]:

- **Primary**: MongoDB ~7.x with Mongoose ODM
- **Schema**: Message model with conversation and user references [Source: architecture/data-models.md]
- **Relationships**: ConversationId -> Conversation, SenderId -> User
- **Indexing**: Efficient queries for conversation messages with pagination

**Testing Framework** [Source: architecture/tech-stack.md]:

- **Primary**: Jest 29.7.0
- **Database Testing**: MongoDB Memory Server for testing
- **API Testing**: Supertest for HTTP endpoint testing

**Authentication** [Source: architecture/tech-stack.md]:

- **JWT**: jsonwebtoken ~9.x for API security
- **Middleware**: Existing authentication middleware from previous stories

### API Specifications

**GET /api/messages/:conversationId** [Source: architecture/api-spesification.md]:

**Request Parameters:**

- `conversationId` (path): String - Conversation ID
- `page` (query): Integer - Page number (default: 1)
- `limit` (query): Integer - Messages per page (default: 20, max: 100)

**Request Example:**

```javascript
GET /api/messages/conv123?page=1&limit=20
Authorization: Bearer <jwt_token>
```

**Response Format:**

```javascript
{
  "success": true,
  "message": "Messages retrieved successfully",
  "data": {
    "messages": [
      {
        "_id": "msg123",
        "conversationId": "conv123",
        "senderId": {
          "_id": "user123",
          "username": "john_doe"
        },
        "content": "Hello there!",
        "isRead": true,
        "createdAt": "2025-01-04T10:35:00.000Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 45,
      "totalPages": 3,
      "hasNext": true,
      "hasPrev": false
    }
  }
}
```

**Error Responses:**

- `401`: Unauthorized (missing/invalid JWT)
- `403`: Forbidden (user not participant in conversation)
- `404`: Conversation not found
- `400`: Invalid pagination parameters

### Security Considerations

**Authentication & Authorization**:

- JWT token required for endpoint
- Users can only access messages from conversations they participate in
- Conversation access validation before message retrieval
- Input sanitization and validation

### Database Schema Design

**Message Model**:

```javascript
{
  _id: ObjectId,
  conversationId: ObjectId, // Reference to Conversation
  senderId: ObjectId, // Reference to User
  content: String, // Message text content
  isRead: Boolean, // Read status (default: false)
  createdAt: Date,
  updatedAt: Date
}
```

**Indexes**:

- `conversationId` index for efficient message queries
- Compound index on `conversationId + createdAt` for pagination
- `senderId` index for sender-based queries

### File Locations

**Based on Project Structure** [Source: architecture/project-structure.md]:

- **Model**: `src/models/message.model.ts` (Already exists)
- **Service**: `src/services/message.service.ts` (To be created)
- **Controller**: `src/api/controllers/message.controller.ts` (To be created)
- **Routes**: `src/api/routes/message.routes.ts` (To be created)
- **DTO**: `src/api/dto/message.dto.ts` (To be created)
- **Middleware**: Use existing `src/api/middlewares/auth.middleware.ts`
- **Types**: `src/types/message.types.ts` (If needed for complex types)

### Coding Standards

**TypeScript Requirements** [Source: architecture/coding-standarts.md]:

- Use `async/await` syntax for all asynchronous operations
- Explicit return types for all public functions
- Use `AppError` class for operational errors
- Repository pattern for database interactions
- ES Modules (`import`/`export`) syntax
- Avoid `any` type - use specific interfaces/types

**Naming Conventions** [Source: architecture/coding-standarts.md]:

- Variables/Functions: `camelCase`
- Classes/Interfaces: `PascalCase`
- Files: `kebab-case`
- Database Models: `PascalCase`

### Integration Points

**Existing Systems**:

- Conversation model for access validation
- User model for sender information
- JWT authentication middleware
- Error handling with AppError
- Logger for message events
- MongoDB with Mongoose ODM

### Business Logic

**Message Retrieval**:

1. Validate conversation ID format
2. Check if conversation exists
3. Verify user is participant in conversation
4. Retrieve messages with pagination
5. Populate sender information
6. Sort by creation date (newest first)
7. Return paginated response

### Pagination Implementation

**Query Parameters**:

- `page`: Page number (default: 1)
- `limit`: Messages per page (default: 20, max: 100)

**Response Format**:

- Include pagination metadata
- Calculate total pages and navigation flags
- Efficient database queries with skip/limit

## Testing

### Testing Standards

**Test File Locations** [Source: architecture/coding-standarts.md]:

- Unit tests: Same folder as source files with `.spec.ts` extension
- Integration tests: `/tests` directory in project root

**Testing Framework** [Source: architecture/tech-stack.md]:

- **Primary**: Jest 29.7.0
- **Database Testing**: MongoDB Memory Server for testing

**Specific Testing Requirements for This Story**:

- Test message model validation and relationships
- Test message service pagination logic
- Test API endpoints with authentication
- Test access control (only participants can access)
- Test pagination edge cases
- Test error handling scenarios

## Change Log

| Date       | Version | Description            | Author             |
| ---------- | ------- | ---------------------- | ------------------ |
| 2025-01-04 | 1.0     | Initial story creation | Scrum Master (Bob) |

## Dev Agent Record

### Agent Model Used

_To be filled by dev agent_

### Debug Log References

_To be filled by dev agent_

### Completion Notes List

_To be filled by dev agent_

### File List

_To be filled by dev agent_

## QA Results

_Results from QA Agent review will be added here_
