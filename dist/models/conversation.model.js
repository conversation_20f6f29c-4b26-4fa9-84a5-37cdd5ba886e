"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = __importStar(require("mongoose"));
const LastMessageSchema = new mongoose_1.Schema({
    content: {
        type: String,
        required: true,
        trim: true,
        maxlength: 1000
    },
    sender: {
        type: mongoose_1.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    timestamp: {
        type: Date,
        required: true,
        default: Date.now
    }
}, { _id: false });
const ConversationSchema = new mongoose_1.Schema({
    participants: [{
            type: mongoose_1.Schema.Types.ObjectId,
            ref: 'User',
            required: true
        }],
    lastMessage: {
        type: LastMessageSchema,
        required: false
    }
}, {
    timestamps: true,
    toJSON: {
        transform: function (_doc, ret) {
            ret.id = ret._id;
            delete ret._id;
            delete ret.__v;
            return ret;
        }
    }
});
ConversationSchema.index({ participants: 1 });
ConversationSchema.index({ updatedAt: -1 });
ConversationSchema.index({ participants: 1, updatedAt: -1 });
ConversationSchema.pre('validate', function (next) {
    if (this.participants.length !== 2) {
        next(new Error('Conversation must have exactly 2 participants'));
    }
    else {
        next();
    }
});
ConversationSchema.pre('validate', function (next) {
    const [participant1, participant2] = this.participants;
    if (participant1.toString() === participant2.toString()) {
        next(new Error('Participants must be different users'));
    }
    else {
        next();
    }
});
ConversationSchema.statics.findBetweenUsers = function (userId1, userId2) {
    return this.findOne({
        participants: {
            $all: [userId1, userId2],
            $size: 2
        }
    }).populate('participants', 'username email');
};
ConversationSchema.statics.findUserConversations = function (userId) {
    return this.find({
        participants: userId
    })
        .populate('participants', 'username email')
        .populate('lastMessage.sender', 'username')
        .sort({ updatedAt: -1 });
};
ConversationSchema.methods.isParticipant = function (userId) {
    return this.participants.some((participant) => participant.toString() === userId.toString());
};
ConversationSchema.methods.getOtherParticipant = function (userId) {
    const otherParticipant = this.participants.find((participant) => participant.toString() !== userId.toString());
    return otherParticipant || null;
};
ConversationSchema.methods.updateLastMessage = function (content, senderId) {
    this.lastMessage = {
        content,
        sender: senderId,
        timestamp: new Date()
    };
    this.updatedAt = new Date();
    return this.save();
};
const Conversation = mongoose_1.default.model('Conversation', ConversationSchema);
exports.default = Conversation;
//# sourceMappingURL=conversation.model.js.map