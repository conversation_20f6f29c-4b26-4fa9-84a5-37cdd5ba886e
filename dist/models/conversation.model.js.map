{"version": 3, "file": "conversation.model.js", "sourceRoot": "", "sources": ["../../src/models/conversation.model.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,qDAA6D;AAmB7D,MAAM,iBAAiB,GAAG,IAAI,iBAAM,CAAe;IACjD,OAAO,EAAE;QACP,IAAI,EAAE,MAAM;QACZ,QAAQ,EAAE,IAAI;QACd,IAAI,EAAE,IAAI;QACV,SAAS,EAAE,IAAI;KAChB;IACD,MAAM,EAAE;QACN,IAAI,EAAE,iBAAM,CAAC,KAAK,CAAC,QAAQ;QAC3B,GAAG,EAAE,MAAM;QACX,QAAQ,EAAE,IAAI;KACf;IACD,SAAS,EAAE;QACT,IAAI,EAAE,IAAI;QACV,QAAQ,EAAE,IAAI;QACd,OAAO,EAAE,IAAI,CAAC,GAAG;KAClB;CACF,EAAE,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC,CAAC;AAEnB,MAAM,kBAAkB,GAAG,IAAI,iBAAM,CAAgB;IACnD,YAAY,EAAE,CAAC;YACb,IAAI,EAAE,iBAAM,CAAC,KAAK,CAAC,QAAQ;YAC3B,GAAG,EAAE,MAAM;YACX,QAAQ,EAAE,IAAI;SACf,CAAC;IACF,WAAW,EAAE;QACX,IAAI,EAAE,iBAAiB;QACvB,QAAQ,EAAE,KAAK;KAChB;CACF,EAAE;IACD,UAAU,EAAE,IAAI;IAChB,MAAM,EAAE;QACN,SAAS,EAAE,UAAS,IAAS,EAAE,GAAQ;YACrC,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,GAAG,CAAC;YACjB,OAAO,GAAG,CAAC,GAAG,CAAC;YACf,OAAO,GAAG,CAAC,GAAG,CAAC;YACf,OAAO,GAAG,CAAC;QACb,CAAC;KACF;CACF,CAAC,CAAC;AAGH,kBAAkB,CAAC,KAAK,CAAC,EAAE,YAAY,EAAE,CAAC,EAAE,CAAC,CAAC;AAC9C,kBAAkB,CAAC,KAAK,CAAC,EAAE,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;AAC5C,kBAAkB,CAAC,KAAK,CAAC,EAAE,YAAY,EAAE,CAAC,EAAE,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;AAG7D,kBAAkB,CAAC,GAAG,CAAC,UAAU,EAAE,UAAS,IAAI;IAC9C,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QACnC,IAAI,CAAC,IAAI,KAAK,CAAC,+CAA+C,CAAC,CAAC,CAAC;IACnE,CAAC;SAAM,CAAC;QACN,IAAI,EAAE,CAAC;IACT,CAAC;AACH,CAAC,CAAC,CAAC;AAGH,kBAAkB,CAAC,GAAG,CAAC,UAAU,EAAE,UAAS,IAAI;IAC9C,MAAM,CAAC,YAAY,EAAE,YAAY,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC;IACvD,IAAI,YAAY,CAAC,QAAQ,EAAE,KAAK,YAAY,CAAC,QAAQ,EAAE,EAAE,CAAC;QACxD,IAAI,CAAC,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC,CAAC;IAC1D,CAAC;SAAM,CAAC;QACN,IAAI,EAAE,CAAC;IACT,CAAC;AACH,CAAC,CAAC,CAAC;AAGH,kBAAkB,CAAC,OAAO,CAAC,gBAAgB,GAAG,UAAS,OAAuB,EAAE,OAAuB;IACrG,OAAO,IAAI,CAAC,OAAO,CAAC;QAClB,YAAY,EAAE;YACZ,IAAI,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;YACxB,KAAK,EAAE,CAAC;SACT;KACF,CAAC,CAAC,QAAQ,CAAC,cAAc,EAAE,gBAAgB,CAAC,CAAC;AAChD,CAAC,CAAC;AAGF,kBAAkB,CAAC,OAAO,CAAC,qBAAqB,GAAG,UAAS,MAAsB;IAChF,OAAO,IAAI,CAAC,IAAI,CAAC;QACf,YAAY,EAAE,MAAM;KACrB,CAAC;SACD,QAAQ,CAAC,cAAc,EAAE,gBAAgB,CAAC;SAC1C,QAAQ,CAAC,oBAAoB,EAAE,UAAU,CAAC;SAC1C,IAAI,CAAC,EAAE,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;AAC3B,CAAC,CAAC;AAGF,kBAAkB,CAAC,OAAO,CAAC,aAAa,GAAG,UAAS,MAAsB;IACxE,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,WAA2B,EAAE,EAAE,CAC5D,WAAW,CAAC,QAAQ,EAAE,KAAK,MAAM,CAAC,QAAQ,EAAE,CAC7C,CAAC;AACJ,CAAC,CAAC;AAGF,kBAAkB,CAAC,OAAO,CAAC,mBAAmB,GAAG,UAAS,MAAsB;IAC9E,MAAM,gBAAgB,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,WAA2B,EAAE,EAAE,CAC9E,WAAW,CAAC,QAAQ,EAAE,KAAK,MAAM,CAAC,QAAQ,EAAE,CAC7C,CAAC;IACF,OAAO,gBAAgB,IAAI,IAAI,CAAC;AAClC,CAAC,CAAC;AAGF,kBAAkB,CAAC,OAAO,CAAC,iBAAiB,GAAG,UAAS,OAAe,EAAE,QAAwB;IAC/F,IAAI,CAAC,WAAW,GAAG;QACjB,OAAO;QACP,MAAM,EAAE,QAAQ;QAChB,SAAS,EAAE,IAAI,IAAI,EAAE;KACtB,CAAC;IACF,IAAI,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;IAC5B,OAAO,IAAI,CAAC,IAAI,EAAE,CAAC;AACrB,CAAC,CAAC;AAOF,MAAM,YAAY,GAAG,kBAAQ,CAAC,KAAK,CAAoC,cAAc,EAAE,kBAAkB,CAAC,CAAC;AAE3G,kBAAe,YAAY,CAAC"}