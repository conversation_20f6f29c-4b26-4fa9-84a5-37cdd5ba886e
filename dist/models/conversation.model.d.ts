import mongoose, { Document, Types } from 'mongoose';
export interface ILastMessage {
    content: string;
    sender: Types.ObjectId;
    timestamp: Date;
}
export interface IConversation extends Document {
    _id: Types.ObjectId;
    participants: Types.ObjectId[];
    createdAt: Date;
    updatedAt: Date;
    lastMessage?: ILastMessage;
    isParticipant(userId: Types.ObjectId): boolean;
    getOtherParticipant(userId: Types.ObjectId): Types.ObjectId | null;
    updateLastMessage(content: string, senderId: Types.ObjectId): Promise<IConversation>;
}
export interface IConversationModel extends mongoose.Model<IConversation> {
    findBetweenUsers(userId1: Types.ObjectId, userId2: Types.ObjectId): Promise<IConversation | null>;
    findUserConversations(userId: Types.ObjectId): Promise<IConversation[]>;
}
declare const Conversation: IConversationModel;
export default Conversation;
//# sourceMappingURL=conversation.model.d.ts.map