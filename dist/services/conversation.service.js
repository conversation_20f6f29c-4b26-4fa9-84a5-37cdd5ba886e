"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.conversationService = exports.ConversationService = void 0;
const mongoose_1 = require("mongoose");
const conversation_model_1 = __importDefault(require("../models/conversation.model"));
const user_model_1 = __importDefault(require("../models/user.model"));
const app_error_1 = require("../utils/app-error");
const logger_1 = __importDefault(require("../utils/logger"));
class ConversationService {
    async createOrFindConversation(data) {
        try {
            const { currentUserId, participantId } = data;
            if (!mongoose_1.Types.ObjectId.isValid(currentUserId) || !mongoose_1.Types.ObjectId.isValid(participantId)) {
                throw new app_error_1.AppError('Invalid user ID format', 400);
            }
            if (currentUserId === participantId) {
                throw new app_error_1.AppError('Cannot create conversation with yourself', 400);
            }
            const currentUserObjectId = new mongoose_1.Types.ObjectId(currentUserId);
            const participantObjectId = new mongoose_1.Types.ObjectId(participantId);
            const participantUser = await user_model_1.default.findById(participantObjectId);
            if (!participantUser) {
                throw new app_error_1.AppError('Participant user not found', 404);
            }
            const existingConversation = await conversation_model_1.default.findBetweenUsers(currentUserObjectId, participantObjectId);
            if (existingConversation) {
                logger_1.default.info('Existing conversation found', {
                    conversationId: existingConversation._id,
                    participants: [currentUserId, participantId],
                    category: 'conversation'
                });
                return existingConversation;
            }
            const newConversation = new conversation_model_1.default({
                participants: [currentUserObjectId, participantObjectId]
            });
            const savedConversation = await newConversation.save();
            await savedConversation.populate('participants', 'username email');
            logger_1.default.info('New conversation created', {
                conversationId: savedConversation._id,
                participants: [currentUserId, participantId],
                category: 'conversation'
            });
            return savedConversation;
        }
        catch (error) {
            logger_1.default.error('Failed to create or find conversation', error, {
                currentUserId: data.currentUserId,
                participantId: data.participantId
            });
            throw error;
        }
    }
    async getUserConversations(userId) {
        try {
            if (!mongoose_1.Types.ObjectId.isValid(userId)) {
                throw new app_error_1.AppError('Invalid user ID format', 400);
            }
            const userObjectId = new mongoose_1.Types.ObjectId(userId);
            const conversations = await conversation_model_1.default.findUserConversations(userObjectId);
            const conversationList = conversations.map(conversation => {
                const participants = conversation.participants
                    .filter((participant) => participant._id.toString() !== userId)
                    .map((participant) => ({
                    id: participant._id.toString(),
                    username: participant.username,
                    email: participant.email
                }));
                const result = {
                    id: conversation._id.toString(),
                    participants,
                    createdAt: conversation.createdAt,
                    updatedAt: conversation.updatedAt
                };
                if (conversation.lastMessage) {
                    result.lastMessage = {
                        content: conversation.lastMessage.content,
                        sender: {
                            id: conversation.lastMessage.sender._id?.toString() || conversation.lastMessage.sender.toString(),
                            username: conversation.lastMessage.sender.username || 'Unknown'
                        },
                        timestamp: conversation.lastMessage.timestamp
                    };
                }
                return result;
            });
            logger_1.default.info('User conversations retrieved', {
                userId,
                conversationCount: conversationList.length,
                category: 'conversation'
            });
            return conversationList;
        }
        catch (error) {
            logger_1.default.error('Failed to get user conversations', error, { userId });
            throw error;
        }
    }
    async getConversationById(conversationId, userId) {
        try {
            if (!mongoose_1.Types.ObjectId.isValid(conversationId) || !mongoose_1.Types.ObjectId.isValid(userId)) {
                throw new app_error_1.AppError('Invalid ID format', 400);
            }
            const conversation = await conversation_model_1.default.findById(conversationId);
            if (!conversation) {
                throw new app_error_1.AppError('Conversation not found', 404);
            }
            const userObjectId = new mongoose_1.Types.ObjectId(userId);
            if (!conversation.isParticipant(userObjectId)) {
                throw new app_error_1.AppError('Access denied: You are not a participant in this conversation', 403);
            }
            await conversation.populate('participants', 'username email');
            return conversation;
        }
        catch (error) {
            logger_1.default.error('Failed to get conversation by ID', error, {
                conversationId,
                userId
            });
            throw error;
        }
    }
    async updateLastMessage(conversationId, content, senderId) {
        try {
            if (!mongoose_1.Types.ObjectId.isValid(conversationId) || !mongoose_1.Types.ObjectId.isValid(senderId)) {
                throw new app_error_1.AppError('Invalid ID format', 400);
            }
            const conversation = await conversation_model_1.default.findById(conversationId);
            if (!conversation) {
                throw new app_error_1.AppError('Conversation not found', 404);
            }
            const senderObjectId = new mongoose_1.Types.ObjectId(senderId);
            if (!conversation.isParticipant(senderObjectId)) {
                throw new app_error_1.AppError('Access denied: You are not a participant in this conversation', 403);
            }
            await conversation.updateLastMessage(content, senderObjectId);
            logger_1.default.info('Conversation last message updated', {
                conversationId,
                senderId,
                category: 'conversation'
            });
            return conversation;
        }
        catch (error) {
            logger_1.default.error('Failed to update last message', error, {
                conversationId,
                senderId
            });
            throw error;
        }
    }
    async deleteConversation(conversationId, userId) {
        try {
            if (!mongoose_1.Types.ObjectId.isValid(conversationId) || !mongoose_1.Types.ObjectId.isValid(userId)) {
                throw new app_error_1.AppError('Invalid ID format', 400);
            }
            const conversation = await conversation_model_1.default.findById(conversationId);
            if (!conversation) {
                throw new app_error_1.AppError('Conversation not found', 404);
            }
            const userObjectId = new mongoose_1.Types.ObjectId(userId);
            if (!conversation.isParticipant(userObjectId)) {
                throw new app_error_1.AppError('Access denied: You are not a participant in this conversation', 403);
            }
            await conversation_model_1.default.findByIdAndDelete(conversationId);
            logger_1.default.info('Conversation deleted', {
                conversationId,
                userId,
                category: 'conversation'
            });
        }
        catch (error) {
            logger_1.default.error('Failed to delete conversation', error, {
                conversationId,
                userId
            });
            throw error;
        }
    }
}
exports.ConversationService = ConversationService;
exports.conversationService = new ConversationService();
exports.default = exports.conversationService;
//# sourceMappingURL=conversation.service.js.map