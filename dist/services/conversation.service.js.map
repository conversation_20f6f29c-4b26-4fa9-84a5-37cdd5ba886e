{"version": 3, "file": "conversation.service.js", "sourceRoot": "", "sources": ["../../src/services/conversation.service.ts"], "names": [], "mappings": ";;;;;;AAAA,uCAAiC;AACjC,sFAA2E;AAC3E,sEAAwC;AACxC,kDAA8C;AAC9C,6DAAqC;AA0BrC,MAAa,mBAAmB;IACvB,KAAK,CAAC,wBAAwB,CAAC,IAA4B;QAChE,IAAI,CAAC;YACH,MAAM,EAAE,aAAa,EAAE,aAAa,EAAE,GAAG,IAAI,CAAC;YAG9C,IAAI,CAAC,gBAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,gBAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE,CAAC;gBACrF,MAAM,IAAI,oBAAQ,CAAC,wBAAwB,EAAE,GAAG,CAAC,CAAC;YACpD,CAAC;YAED,IAAI,aAAa,KAAK,aAAa,EAAE,CAAC;gBACpC,MAAM,IAAI,oBAAQ,CAAC,0CAA0C,EAAE,GAAG,CAAC,CAAC;YACtE,CAAC;YAED,MAAM,mBAAmB,GAAG,IAAI,gBAAK,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;YAC9D,MAAM,mBAAmB,GAAG,IAAI,gBAAK,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;YAG9D,MAAM,eAAe,GAAG,MAAM,oBAAI,CAAC,QAAQ,CAAC,mBAAmB,CAAC,CAAC;YACjE,IAAI,CAAC,eAAe,EAAE,CAAC;gBACrB,MAAM,IAAI,oBAAQ,CAAC,4BAA4B,EAAE,GAAG,CAAC,CAAC;YACxD,CAAC;YAGD,MAAM,oBAAoB,GAAG,MAAM,4BAAY,CAAC,gBAAgB,CAC9D,mBAAmB,EACnB,mBAAmB,CACpB,CAAC;YAEF,IAAI,oBAAoB,EAAE,CAAC;gBACzB,gBAAM,CAAC,IAAI,CAAC,6BAA6B,EAAE;oBACzC,cAAc,EAAE,oBAAoB,CAAC,GAAG;oBACxC,YAAY,EAAE,CAAC,aAAa,EAAE,aAAa,CAAC;oBAC5C,QAAQ,EAAE,cAAc;iBACzB,CAAC,CAAC;gBACH,OAAO,oBAAoB,CAAC;YAC9B,CAAC;YAGD,MAAM,eAAe,GAAG,IAAI,4BAAY,CAAC;gBACvC,YAAY,EAAE,CAAC,mBAAmB,EAAE,mBAAmB,CAAC;aACzD,CAAC,CAAC;YAEH,MAAM,iBAAiB,GAAG,MAAM,eAAe,CAAC,IAAI,EAAE,CAAC;YAGvD,MAAM,iBAAiB,CAAC,QAAQ,CAAC,cAAc,EAAE,gBAAgB,CAAC,CAAC;YAEnE,gBAAM,CAAC,IAAI,CAAC,0BAA0B,EAAE;gBACtC,cAAc,EAAE,iBAAiB,CAAC,GAAG;gBACrC,YAAY,EAAE,CAAC,aAAa,EAAE,aAAa,CAAC;gBAC5C,QAAQ,EAAE,cAAc;aACzB,CAAC,CAAC;YAEH,OAAO,iBAAiB,CAAC;QAE3B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,gBAAM,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAc,EAAE;gBACpE,aAAa,EAAE,IAAI,CAAC,aAAa;gBACjC,aAAa,EAAE,IAAI,CAAC,aAAa;aAClC,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,oBAAoB,CAAC,MAAc;QAC9C,IAAI,CAAC;YAEH,IAAI,CAAC,gBAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;gBACpC,MAAM,IAAI,oBAAQ,CAAC,wBAAwB,EAAE,GAAG,CAAC,CAAC;YACpD,CAAC;YAED,MAAM,YAAY,GAAG,IAAI,gBAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YAGhD,MAAM,aAAa,GAAG,MAAM,4BAAY,CAAC,qBAAqB,CAAC,YAAY,CAAC,CAAC;YAG7E,MAAM,gBAAgB,GAA2B,aAAa,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE;gBAChF,MAAM,YAAY,GAAG,YAAY,CAAC,YAAY;qBAC3C,MAAM,CAAC,CAAC,WAAgB,EAAE,EAAE,CAAC,WAAW,CAAC,GAAG,CAAC,QAAQ,EAAE,KAAK,MAAM,CAAC;qBACnE,GAAG,CAAC,CAAC,WAAgB,EAAE,EAAE,CAAC,CAAC;oBAC1B,EAAE,EAAE,WAAW,CAAC,GAAG,CAAC,QAAQ,EAAE;oBAC9B,QAAQ,EAAE,WAAW,CAAC,QAAQ;oBAC9B,KAAK,EAAE,WAAW,CAAC,KAAK;iBACzB,CAAC,CAAC,CAAC;gBAEN,MAAM,MAAM,GAAyB;oBACnC,EAAE,EAAE,YAAY,CAAC,GAAG,CAAC,QAAQ,EAAE;oBAC/B,YAAY;oBACZ,SAAS,EAAE,YAAY,CAAC,SAAS;oBACjC,SAAS,EAAE,YAAY,CAAC,SAAS;iBAClC,CAAC;gBAGF,IAAI,YAAY,CAAC,WAAW,EAAE,CAAC;oBAC7B,MAAM,CAAC,WAAW,GAAG;wBACnB,OAAO,EAAE,YAAY,CAAC,WAAW,CAAC,OAAO;wBACzC,MAAM,EAAE;4BACN,EAAE,EAAE,YAAY,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,EAAE,QAAQ,EAAE,IAAI,YAAY,CAAC,WAAW,CAAC,MAAM,CAAC,QAAQ,EAAE;4BACjG,QAAQ,EAAG,YAAY,CAAC,WAAW,CAAC,MAAc,CAAC,QAAQ,IAAI,SAAS;yBACzE;wBACD,SAAS,EAAE,YAAY,CAAC,WAAW,CAAC,SAAS;qBAC9C,CAAC;gBACJ,CAAC;gBAED,OAAO,MAAM,CAAC;YAChB,CAAC,CAAC,CAAC;YAEH,gBAAM,CAAC,IAAI,CAAC,8BAA8B,EAAE;gBAC1C,MAAM;gBACN,iBAAiB,EAAE,gBAAgB,CAAC,MAAM;gBAC1C,QAAQ,EAAE,cAAc;aACzB,CAAC,CAAC;YAEH,OAAO,gBAAgB,CAAC;QAE1B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,gBAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAc,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC;YAC7E,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,mBAAmB,CAAC,cAAsB,EAAE,MAAc;QACrE,IAAI,CAAC;YAEH,IAAI,CAAC,gBAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,gBAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;gBAC/E,MAAM,IAAI,oBAAQ,CAAC,mBAAmB,EAAE,GAAG,CAAC,CAAC;YAC/C,CAAC;YAED,MAAM,YAAY,GAAG,MAAM,4BAAY,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC;YAEjE,IAAI,CAAC,YAAY,EAAE,CAAC;gBAClB,MAAM,IAAI,oBAAQ,CAAC,wBAAwB,EAAE,GAAG,CAAC,CAAC;YACpD,CAAC;YAGD,MAAM,YAAY,GAAG,IAAI,gBAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YAChD,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,YAAY,CAAC,EAAE,CAAC;gBAC9C,MAAM,IAAI,oBAAQ,CAAC,+DAA+D,EAAE,GAAG,CAAC,CAAC;YAC3F,CAAC;YAGD,MAAM,YAAY,CAAC,QAAQ,CAAC,cAAc,EAAE,gBAAgB,CAAC,CAAC;YAE9D,OAAO,YAAY,CAAC;QAEtB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,gBAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAc,EAAE;gBAC/D,cAAc;gBACd,MAAM;aACP,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,iBAAiB,CAC5B,cAAsB,EACtB,OAAe,EACf,QAAgB;QAEhB,IAAI,CAAC;YAEH,IAAI,CAAC,gBAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,gBAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACjF,MAAM,IAAI,oBAAQ,CAAC,mBAAmB,EAAE,GAAG,CAAC,CAAC;YAC/C,CAAC;YAED,MAAM,YAAY,GAAG,MAAM,4BAAY,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC;YACjE,IAAI,CAAC,YAAY,EAAE,CAAC;gBAClB,MAAM,IAAI,oBAAQ,CAAC,wBAAwB,EAAE,GAAG,CAAC,CAAC;YACpD,CAAC;YAGD,MAAM,cAAc,GAAG,IAAI,gBAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YACpD,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,cAAc,CAAC,EAAE,CAAC;gBAChD,MAAM,IAAI,oBAAQ,CAAC,+DAA+D,EAAE,GAAG,CAAC,CAAC;YAC3F,CAAC;YAGD,MAAM,YAAY,CAAC,iBAAiB,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC;YAE9D,gBAAM,CAAC,IAAI,CAAC,mCAAmC,EAAE;gBAC/C,cAAc;gBACd,QAAQ;gBACR,QAAQ,EAAE,cAAc;aACzB,CAAC,CAAC;YAEH,OAAO,YAAY,CAAC;QAEtB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,gBAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAc,EAAE;gBAC5D,cAAc;gBACd,QAAQ;aACT,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,kBAAkB,CAAC,cAAsB,EAAE,MAAc;QACpE,IAAI,CAAC;YAEH,IAAI,CAAC,gBAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,gBAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;gBAC/E,MAAM,IAAI,oBAAQ,CAAC,mBAAmB,EAAE,GAAG,CAAC,CAAC;YAC/C,CAAC;YAED,MAAM,YAAY,GAAG,MAAM,4BAAY,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC;YACjE,IAAI,CAAC,YAAY,EAAE,CAAC;gBAClB,MAAM,IAAI,oBAAQ,CAAC,wBAAwB,EAAE,GAAG,CAAC,CAAC;YACpD,CAAC;YAGD,MAAM,YAAY,GAAG,IAAI,gBAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YAChD,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,YAAY,CAAC,EAAE,CAAC;gBAC9C,MAAM,IAAI,oBAAQ,CAAC,+DAA+D,EAAE,GAAG,CAAC,CAAC;YAC3F,CAAC;YAED,MAAM,4BAAY,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;YAErD,gBAAM,CAAC,IAAI,CAAC,sBAAsB,EAAE;gBAClC,cAAc;gBACd,MAAM;gBACN,QAAQ,EAAE,cAAc;aACzB,CAAC,CAAC;QAEL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,gBAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAc,EAAE;gBAC5D,cAAc;gBACd,MAAM;aACP,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;CACF;AAxOD,kDAwOC;AAEY,QAAA,mBAAmB,GAAG,IAAI,mBAAmB,EAAE,CAAC;AAC7D,kBAAe,2BAAmB,CAAC"}