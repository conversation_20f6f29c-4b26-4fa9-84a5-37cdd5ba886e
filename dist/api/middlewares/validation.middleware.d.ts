import { Request, Response, NextFunction } from 'express';
export declare function validationMiddleware<T extends object>(dtoClass: new () => T, source?: 'body' | 'query', skipMissingProperties?: boolean): (req: Request, _res: Response, next: NextFunction) => Promise<void>;
export declare const validateRequest: (req: Request, _res: Response, next: NextFunction) => void;
//# sourceMappingURL=validation.middleware.d.ts.map