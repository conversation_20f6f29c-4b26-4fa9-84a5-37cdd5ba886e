"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateRequest = void 0;
exports.validationMiddleware = validationMiddleware;
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const express_validator_1 = require("express-validator");
const app_error_1 = require("../../utils/app-error");
function validationMiddleware(dtoClass, source = 'body', skipMissingProperties = false) {
    return async (req, _res, next) => {
        try {
            const sourceData = source === 'query' ? req.query : req.body;
            const dto = (0, class_transformer_1.plainToClass)(dtoClass, sourceData);
            const errors = await (0, class_validator_1.validate)(dto, {
                skipMissingProperties,
                whitelist: true,
                forbidNonWhitelisted: false
            });
            if (errors.length > 0) {
                throw new app_error_1.AppError('Validation failed', 400, 'VALIDATION_ERROR');
            }
            if (source === 'query') {
                req.query = dto;
            }
            else {
                req.body = dto;
            }
            next();
        }
        catch (error) {
            if (error instanceof app_error_1.AppError) {
                next(error);
            }
            else {
                next(new app_error_1.AppError('Validation failed', 400, 'VALIDATION_ERROR'));
            }
        }
    };
}
const validateRequest = (req, _res, next) => {
    const errors = (0, express_validator_1.validationResult)(req);
    if (!errors.isEmpty()) {
        const errorMessages = errors.array().map(error => error.msg).join(', ');
        throw new app_error_1.AppError(`Validation failed: ${errorMessages}`, 400, 'VALIDATION_ERROR');
    }
    next();
};
exports.validateRequest = validateRequest;
//# sourceMappingURL=validation.middleware.js.map