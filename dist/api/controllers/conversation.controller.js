"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.conversationController = exports.ConversationController = void 0;
const conversation_service_1 = require("../../services/conversation.service");
const logger_1 = __importDefault(require("../../utils/logger"));
class ConversationController {
    async createConversation(req, res) {
        try {
            const { participantId } = req.body;
            const currentUserId = req.user.userId;
            logger_1.default.info('[CONVERSATION] Create conversation attempt', {
                currentUserId,
                participantId,
                category: 'conversation'
            });
            const conversation = await conversation_service_1.conversationService.createOrFindConversation({
                currentUserId,
                participantId
            });
            const isNewConversation = conversation.createdAt.getTime() === conversation.updatedAt.getTime();
            const message = isNewConversation ? 'Conversation created successfully' : 'Existing conversation found';
            logger_1.default.info('[CONVERSATION] Create conversation successful', {
                conversationId: conversation._id,
                currentUserId,
                participantId,
                isNew: isNewConversation,
                category: 'conversation'
            });
            res.status(isNewConversation ? 201 : 200).json({
                success: true,
                message,
                data: {
                    conversation
                }
            });
        }
        catch (error) {
            logger_1.default.error('[CONVERSATION] Create conversation failed', error, {
                currentUserId: req.user?.userId,
                participantId: req.body?.participantId,
                category: 'conversation'
            });
            throw error;
        }
    }
    async getConversations(req, res) {
        try {
            const userId = req.user.userId;
            const { page = 1, limit = 20, search } = req.query;
            logger_1.default.info('[CONVERSATION] Get conversations attempt', {
                userId,
                page,
                limit,
                search,
                category: 'conversation'
            });
            const conversations = await conversation_service_1.conversationService.getUserConversations(userId);
            let filteredConversations = conversations;
            if (search && typeof search === 'string') {
                const searchLower = search.toLowerCase();
                filteredConversations = conversations.filter(conversation => conversation.participants.some(participant => participant.username.toLowerCase().includes(searchLower) ||
                    participant.email.toLowerCase().includes(searchLower)));
            }
            const pageNum = Number(page);
            const limitNum = Number(limit);
            const startIndex = (pageNum - 1) * limitNum;
            const endIndex = startIndex + limitNum;
            const paginatedConversations = filteredConversations.slice(startIndex, endIndex);
            logger_1.default.info('[CONVERSATION] Get conversations successful', {
                userId,
                totalConversations: conversations.length,
                filteredCount: filteredConversations.length,
                returnedCount: paginatedConversations.length,
                page: pageNum,
                limit: limitNum,
                category: 'conversation'
            });
            res.status(200).json({
                success: true,
                message: 'Conversations retrieved successfully',
                data: {
                    conversations: paginatedConversations,
                    pagination: {
                        page: pageNum,
                        limit: limitNum,
                        total: filteredConversations.length,
                        totalPages: Math.ceil(filteredConversations.length / limitNum),
                        hasNext: endIndex < filteredConversations.length,
                        hasPrev: pageNum > 1
                    }
                }
            });
        }
        catch (error) {
            logger_1.default.error('[CONVERSATION] Get conversations failed', error, {
                userId: req.user?.userId || 'unknown',
                category: 'conversation'
            });
            throw error;
        }
    }
    async getConversation(req, res) {
        try {
            const { conversationId } = req.params;
            const userId = req.user.userId;
            logger_1.default.info('[CONVERSATION] Get conversation attempt', {
                conversationId,
                userId,
                category: 'conversation'
            });
            const conversation = await conversation_service_1.conversationService.getConversationById(conversationId, userId);
            logger_1.default.info('[CONVERSATION] Get conversation successful', {
                conversationId,
                userId,
                category: 'conversation'
            });
            res.status(200).json({
                success: true,
                message: 'Conversation retrieved successfully',
                data: {
                    conversation
                }
            });
        }
        catch (error) {
            logger_1.default.error('[CONVERSATION] Get conversation failed', error, {
                conversationId: req.params?.conversationId || 'unknown',
                userId: req.user?.userId || 'unknown',
                category: 'conversation'
            });
            throw error;
        }
    }
    async deleteConversation(req, res) {
        try {
            const { conversationId } = req.params;
            const userId = req.user.userId;
            logger_1.default.info('[CONVERSATION] Delete conversation attempt', {
                conversationId,
                userId,
                category: 'conversation'
            });
            await conversation_service_1.conversationService.deleteConversation(conversationId, userId);
            logger_1.default.info('[CONVERSATION] Delete conversation successful', {
                conversationId,
                userId,
                category: 'conversation'
            });
            res.status(200).json({
                success: true,
                message: 'Conversation deleted successfully'
            });
        }
        catch (error) {
            logger_1.default.error('[CONVERSATION] Delete conversation failed', error, {
                conversationId: req.params?.conversationId || 'unknown',
                userId: req.user?.userId || 'unknown',
                category: 'conversation'
            });
            throw error;
        }
    }
}
exports.ConversationController = ConversationController;
exports.conversationController = new ConversationController();
exports.default = exports.conversationController;
//# sourceMappingURL=conversation.controller.js.map