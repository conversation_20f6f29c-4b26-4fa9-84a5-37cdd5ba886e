Backend Developer · Case Study
PROJE

Real-Time Mesajlaşma Sistemi Proje Dokümantasyonu

Genel Bilgilendirme
Bu proje kapsamında, kullanıcıların birbiriyle gerçek zamanlı mesajlaşabileceği basit bir kullanıcı
sistemi geliştirilecektir. Sistem, modern web teknolojileri kullanılarak ölçeklenebilir ve performanslı
bir yapıda tasarlanacaktır.


Kullanılacak Teknolojiler

Bu projede aşağıdaki teknolojilerin aktif olarak kullanılması gerekmektedir
Node.js - Sunucu tarafı JavaScript çalışma ortam
Express.js - Web uygulama framework'
MongoDB - NoSQL veritaban
RabbitMQ - Mesaj kuyruğu sistem
Redis - In-memory veri yapısı depos
JWT - JSON Web Token kimlik doğrulam
Socket.IO - Gerçek zamanlı iletişim kütüphanes
Cron - Zamanlanmış görev yöneticisi

Proje Gereksinimleri
1. Veri Modelleri
Sistemde aşağıdaki veri modelleri oluşturulacaktır
User - Kullanıcı bilgiler
Conversation - Konuşma bilgiler
Message - Mesaj bilgiler
AutoMessage - Otomatik mesaj bilgileri

2. Kullanıcı Yönetimi API'leri
I. Kimlik Doğrulama Endpoint'leri
`POST /api/auth/register` - Yeni kullanıcı kayd
`POST /api/auth/login` - Kullanıcı giriş işlem
`POST /api/auth/refresh` - Access token yenilem
`POST /api/auth/logout` - Kullanıcı çıkış işlem
`GET /api/user/list` - Sistemdeki kullanıcıları listelem
`GET /api/auth/me` - Kullanıcı profil bilgilerini görüntüleme

3. Mesajlaşma API'leri
I. Mesaj Yönetimi Endpoint'leri

Ürünün tasarım yönlendirmes
Ton, renk, UX öncelikleri

4. Socket.IO Event'leri
I. Gerçek Zamanlı İletişim Event'leri
`connection` - Kullanıcının sisteme bağlanmas
`join_ room` - Belirli bir konuşma odasına katılm
`send _ message` - Gerçek zamanlı mesaj gönderm
`message_ received` - Mesaj alındı bildirim
`user_online` - Kullanıcının online durumu bildirim
`disconnect` - Kullanıcının sistemden ayrılması

Geliştirme Gereksinimleri
1. Aut

hentication Middleware
I

Güvenli AP erişimi için aşağıdaki özellikleri içeren middleware geliştirmeniz gerekmektedir

JWT token doğrulama mekanizmas
Authorization header'dan token çıkarm
Doğrulanmış kullanıcı bilgilerini request nesnesine eklem
Hata durumlarında uygun HTTP status kodları döndürme
2.

RabbitMQ Kuyruk Sistemi

Asenkron mesaj işleme için aşağıdaki bileşenleri içeren sistem
Otomatik mesajlar için özel kuyruk yapıs

(üretici ) ve consumer (tüketici ) servisler
Hata yönetimi ve tekrar deneme (retry) mekanizması
Message producer

3.

Cron Job - Otomatik Mesaj Sistemi

Zamanlanmış görev yönetimi için aşağıdaki özellikleri içeren sistem
Her gece saat 02:00'da çalışan otomatik göre
Rastgele kullanıcı eşleştirme algoritmas
Mesaj içeriği üretimi ve veritabanına kaydetm

RabbitMQ kuyruğuna mesaj ekleme işlemi
4. Redis Ser visleri
Performans optimizasyonu için aşağıdaki cache sistemleri
Kullanıcı online/offline durumu takib

'

Konuşma verilerinin cache lenmes
Session yönetim
Geçici veri saklama mekanizması

5. Socket.IO Implementasyonu
Gerçek zamanlı iletişim için aşağıdaki özellikleri içeren sistem

JWT tabanlı kullanıcı kimlik doğrulamas
Gerçek zamanlı mesaj gönderim sistem
Kullanıcı online durumu takib
Oda

(room) yönetimi ve kullanıcı gruplama

Sistem

Akışı ve Otomasyon

Bu bölümde kullanıcıların gerçekleştirebileceği temel işlemler ve sistemin arka planda otomatik
olarak yürüteceği süreçler detaylandırılmıştır.

İ

1. Kullanıcı şlemleri

yıt ve Kimlik Doğrulama

I. Ka

Kullanıcılar username, email ve password bilgileri ile sisteme kayıt olabilirler. Başarılı giriş
işlemi sonrasında

II.

JWT tabanlı Access Token ve Refresh Token alırlar.

Profil Yönetimi
'

Kullanıcılar kimlik doğrulaması gerektiren endpoint ler üzerinden kendi profil bilgilerini
görüntüleyebilir ve güncelleyebilirler.

III. Gerçek Zamanlı Mesajlaşma

I

Socket. O teknolojisi kullanılarak anlık mesaj gönderim ve alma işlemler
Karşı tarafın yazma durumunu gösteren typing event

'

Mesaj okundu bilgisi ve anlık bildirimler

IV. Mesaj Geçmişi Yönetimi
Kullanıcılar önceki konuşmalarını ve bu konuşmalardaki tüm mesajları

RESTful API endpoint'leri

aracılığıyla görüntüleyebilirler.

V. Güvenli Oturum Sonlandırma

Sistem çıkışı sırasında

JWT token'ların geçersiz kılınması ve güvenli oturum sonlandırma

işlemleri.

2. Otomatik Sistem Süreçleri

Bu bölüm projenin en kritik kısımlarından biridir ve zamanlanmış görev yönetimi ile asenkron işlem
becerilerini test etmeyi amaçlar. Sistem, üç ayrı ve yönetilebilir aşamada çalışır: Planlama,
Kuyruğa Alma ve İşleme.

1. Adım: Mesaj Planlama Servisi (Cron Job - Gece 02:00)
Amaç: Aktif kullanıcıları otomatik olarak eşleştirerek gönderilecek mesajları toplu halde
hazırlamak.

İşlem Süreci
Her gece saat 02:00'da otomatik olarak tetikleni
Veritabanındaki tüm aktif kullanıcıları çeke
Kullanıcı listesini rastgele karıştırır (shuffle algoritması
Karıştırılmış listeyi ikişerli gruplara ayırarak (gönderici, alıcı) çiftleri oluşturu
Her çift için rastgele mesaj içeriği hazırla
Gönderim için gelecek tarih (sendDate) belirle
Tüm bilgileri AutoMessage koleksiyonuna kaydeder

2. Adım: Kuyruk Yönetimi Servisi (Worker Cron Job - Dakikada Bir)
Amaç: Gönderim zamanı gelen mesajları tespit edip

RabbitMQ sistemine yönlendirmek.

İşlem Süreci
Her dakika otomatik olarak çalışı
AutoMessage koleksiyonunda sendDate'i geçmiş ve isQueued: false olan mesajları ara
Tespit edilen mesajları RabbitMQ'daki message_sending_queue kuyruğuna gönderi
Aynı mesajın tekrar işlenmemesi için AutoMessage kaydını isQueued: true olarak günceller

3. Adım: Mesaj Dağıtım Servisi (RabbitMQ Consumer)
Amaç: Kuyruktaki mesajları işleyerek alıcılara ulaştırmak.
İşlem Süreci
message_sending_queue kuyruğunu sürekli dinle
Kuyruğa gelen görevleri anında alır ve işle
Görev bilgilerine göre yeni Message dökümanı oluşturur ve veritabanına kaydede

Socket.IO üzerinden alıcıya message_received eventi ile anlık bildirim gönderi
AutoMessage kaydını isSent: true olarak güncelleyerek işlemi tamamlar
Sistem Avantajları:

Bu üç aşamalı yapı sayesinde görevlerin zamanlanması, işleme alınacakların tespiti ve gerçek
gönderim işlemlerinin birbirinden ayrıştırılması sağlanır. Bu yaklaşım ölçeklenebilir, hataya
dayanıklı ve yönetilebilir bir otomatik mesajlaşma sistemi oluşturur.

3. Online Kullanıcı

Takip Sistemi

I

Bu sistem Socket. O ve

Redis teknolojilerini kullanarak kullanıcıların online durumlarını gerçek

zamanlı olarak takip eder.

I. Kullanı

cı Bağlantı Yönetimi

Kullanıcılar username, email ve password bilgileri ile sisteme kayıt olabilirler. Başarılı giriş
işlemi sonrasında

JWT tabanlı Access Token ve Refresh Token alırlar.

ğlandığında (connection eventi )
Socket.IO üzerinden gelen bağlantı JWT token ile doğrulanı

Kullanıcı Sisteme Ba

Kimlik doğrulaması başarılı olan kullanıcı Redis'teki online kullanıcılar listesine
yapısı) ekleni

(Set veri

Diğer kullanıcılara kullanıcının online olduğu bilgisi broadcast edilir

Kullanıcı Sistemden

Ayrıldığında (disconnect eventi )

Bağlantısı kesilen kullanıcının kimliği Redis'teki online listesinden kaldırılı
Diğer kullanıcılara kullanıcının offline olduğu bilgisi iletilir

guları

II. Online Durum Sor

Kullanıcılar username, email ve password bilgileri ile sisteme kayıt olabilirler. Başarılı giriş
işlemi sonrasında

JWT tabanlı Access Token ve Refresh Token alırlar

Anlık Online Kullanıcı Sayısı: Redis'teki online kullanıcılar Set'inin eleman sayısı
sorgulanarak anlık online kullanıcı sayısı alınır

Belirli Kullanıcının Online Durumu: Redis Set'inde belirli bir kullanıcı ID'sinin varlığı kontrol
edilerek o kullanıcının online/offline durumu tespit edilir

Online Kullanıcı Listesi: Bir test endpointi ile istatistik amaçlı Redis Set'indeki tüm online
kullanıcı

ID'leri listelenir.

Değerlendirilecek Noktalar
Backend Geliştirme: Node.js ve Express.js ile API geliştirme beceriler
Veritabanı Yönetimi: MongoDB ile NoSQL veritabanı tasarımı ve işlemler
Güvenlik: JWT tabanlı kimlik doğrulama ve yetkilendirme implementasyon
Gerçek Zamanlı İletişim: Socket.IO ile real-time mesajlaşma sistem
Asenkron İşlemler: RabbitMQ ile message queue sistemi kurulum
Cache Yönetimi: Redis ile performans optimizasyon
Zamanlanmış Görevler: Cron job'ları ile otomatik sistem süreçler
Kod Kalitesi: Temiz kod yazma prensipleri ve best practice'ler

Bonus Kriterler:

Bu özelliklerin kullanılması bonus puan olacaktı

r


Temel Bonus Özellikler
Logger kullanımı (Winston, Pino vs.
Elastic Search kullanımı (Mesaj arama, indeksleme
Swagger/OpenAPI entegrasyonu (API dokümantasyonu
Error Yakalama (Sentry vs.)

Güvenlik ve Performanc
Rate Limiting middlewar
Input Validation ve sanitizatio
Database indexing ve optimizasyo
Redis caching stratejiler
Security headers (Helmet vs.)

Bu proje tamamen kurgusaldır. Gerçek bir ürün için kullanılmayacaktır. Çalışmalarınız yalnızca
değerlendirme amacıyla incelenecek ve ticari bir çalışmada yer almayacaktır.

