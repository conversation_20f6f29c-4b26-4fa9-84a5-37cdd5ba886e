const axios = require("axios");

// Load environment variables
require("dotenv").config();

const SERVER_URL = "http://localhost:3002";

async function testConversationAPI() {
  console.log("🔄 Testing Conversation API...");

  try {
    // 1. Register two test users
    console.log("📝 Registering test users...");
    const timestamp = Date.now();

    const user1Response = await axios.post(`${SERVER_URL}/api/auth/register`, {
      username: `testuser1_${timestamp}`,
      email: `testuser1_${timestamp}@example.com`,
      password: "Password123",
    });

    const user2Response = await axios.post(`${SERVER_URL}/api/auth/register`, {
      username: `testuser2_${timestamp}`,
      email: `testuser2_${timestamp}@example.com`,
      password: "Password123",
    });

    console.log("✅ Users registered successfully");

    // 2. Login both users to get JWT tokens
    console.log("🔐 Logging in users...");
    const user1LoginResponse = await axios.post(
      `${SERVER_URL}/api/auth/login`,
      {
        email: `testuser1_${timestamp}@example.com`,
        password: "Password123",
      }
    );

    const user2LoginResponse = await axios.post(
      `${SERVER_URL}/api/auth/login`,
      {
        email: `testuser2_${timestamp}@example.com`,
        password: "Password123",
      }
    );

    const user1Token = user1LoginResponse.data.data.accessToken;
    const user2Token = user2LoginResponse.data.data.accessToken;

    // Extract user IDs from JWT tokens
    const user1Payload = JSON.parse(
      Buffer.from(user1Token.split(".")[1], "base64").toString()
    );
    const user2Payload = JSON.parse(
      Buffer.from(user2Token.split(".")[1], "base64").toString()
    );

    console.log("✅ Login successful for both users");
    console.log(`   User 1 ID: ${user1Payload.userId}`);
    console.log(`   User 2 ID: ${user2Payload.userId}`);

    // 3. Test creating a conversation
    console.log("💬 Creating conversation...");
    const createConversationResponse = await axios.post(
      `${SERVER_URL}/api/conversations`,
      {
        participantId: user2Payload.userId,
      },
      {
        headers: {
          Authorization: `Bearer ${user1Token}`,
        },
      }
    );

    const conversation = createConversationResponse.data.data.conversation;
    console.log("✅ Conversation created successfully");
    console.log(`   Conversation ID: ${conversation.id}`);
    console.log(`   Participants: ${conversation.participants.length}`);

    // 4. Test creating the same conversation again (should return existing)
    console.log("🔄 Testing duplicate conversation creation...");
    const duplicateConversationResponse = await axios.post(
      `${SERVER_URL}/api/conversations`,
      {
        participantId: user2Payload.userId,
      },
      {
        headers: {
          Authorization: `Bearer ${user1Token}`,
        },
      }
    );

    console.log("✅ Duplicate conversation handled correctly");
    console.log(`   Message: ${duplicateConversationResponse.data.message}`);
    console.log(
      `   Same ID: ${duplicateConversationResponse.data.data.conversation.id === conversation.id}`
    );

    // 5. Test getting conversations for user 1
    console.log("📋 Getting conversations for user 1...");
    const user1ConversationsResponse = await axios.get(
      `${SERVER_URL}/api/conversations`,
      {
        headers: {
          Authorization: `Bearer ${user1Token}`,
        },
      }
    );

    console.log("✅ User 1 conversations retrieved");
    console.log(
      `   Count: ${user1ConversationsResponse.data.data.conversations.length}`
    );
    console.log(
      `   Pagination: ${JSON.stringify(user1ConversationsResponse.data.data.pagination)}`
    );

    // 6. Test getting conversations for user 2
    console.log("📋 Getting conversations for user 2...");
    const user2ConversationsResponse = await axios.get(
      `${SERVER_URL}/api/conversations`,
      {
        headers: {
          Authorization: `Bearer ${user2Token}`,
        },
      }
    );

    console.log("✅ User 2 conversations retrieved");
    console.log(
      `   Count: ${user2ConversationsResponse.data.data.conversations.length}`
    );

    // 7. Test getting specific conversation
    console.log("🔍 Getting specific conversation...");
    const specificConversationResponse = await axios.get(
      `${SERVER_URL}/api/conversations/${conversation.id}`,
      {
        headers: {
          Authorization: `Bearer ${user1Token}`,
        },
      }
    );

    console.log("✅ Specific conversation retrieved");
    console.log(
      `   ID: ${specificConversationResponse.data.data.conversation.id}`
    );

    // 8. Test pagination
    console.log("📄 Testing pagination...");
    const paginatedResponse = await axios.get(
      `${SERVER_URL}/api/conversations?page=1&limit=5`,
      {
        headers: {
          Authorization: `Bearer ${user1Token}`,
        },
      }
    );

    console.log("✅ Pagination working");
    console.log(`   Page: ${paginatedResponse.data.data.pagination.page}`);
    console.log(`   Limit: ${paginatedResponse.data.data.pagination.limit}`);

    // 9. Test search functionality
    console.log("🔍 Testing search functionality...");
    const searchResponse = await axios.get(
      `${SERVER_URL}/api/conversations?search=testuser2`,
      {
        headers: {
          Authorization: `Bearer ${user1Token}`,
        },
      }
    );

    console.log("✅ Search functionality working");
    console.log(
      `   Search results: ${searchResponse.data.data.conversations.length}`
    );

    // 10. Test error cases
    console.log("❌ Testing error cases...");

    // Test creating conversation with non-existent user
    try {
      await axios.post(
        `${SERVER_URL}/api/conversations`,
        {
          participantId: "507f1f77bcf86cd799439011", // Non-existent ObjectId
        },
        {
          headers: {
            Authorization: `Bearer ${user1Token}`,
          },
        }
      );
      console.log("❌ Should have failed for non-existent user");
    } catch (error) {
      console.log("✅ Correctly rejected non-existent user");
      console.log(
        `   Error: ${error.response?.data?.message || error.message}`
      );
    }

    // Test creating conversation with self
    try {
      await axios.post(
        `${SERVER_URL}/api/conversations`,
        {
          participantId: user1Payload.userId,
        },
        {
          headers: {
            Authorization: `Bearer ${user1Token}`,
          },
        }
      );
      console.log("❌ Should have failed for self conversation");
    } catch (error) {
      console.log("✅ Correctly rejected self conversation");
      console.log(
        `   Error: ${error.response?.data?.message || error.message}`
      );
    }

    // Test accessing conversation without authentication
    try {
      await axios.get(`${SERVER_URL}/api/conversations`);
      console.log("❌ Should have failed without authentication");
    } catch (error) {
      console.log("✅ Correctly rejected unauthenticated request");
      console.log(`   Status: ${error.response?.status || "Unknown"}`);
    }

    // 11. Test deleting conversation
    console.log("🗑️ Testing conversation deletion...");
    const deleteResponse = await axios.delete(
      `${SERVER_URL}/api/conversations/${conversation.id}`,
      {
        headers: {
          Authorization: `Bearer ${user1Token}`,
        },
      }
    );

    console.log("✅ Conversation deleted successfully");
    console.log(`   Message: ${deleteResponse.data.message}`);

    // Verify deletion
    const finalConversationsResponse = await axios.get(
      `${SERVER_URL}/api/conversations`,
      {
        headers: {
          Authorization: `Bearer ${user1Token}`,
        },
      }
    );

    console.log("✅ Deletion verified");
    console.log(
      `   Remaining conversations: ${finalConversationsResponse.data.data.conversations.length}`
    );

    console.log("🎉 All Conversation API tests passed!");
  } catch (error) {
    console.error("❌ Conversation API test failed:", error.message);
    if (error.response) {
      console.error("   Response data:", error.response.data);
      console.error("   Status:", error.response.status);
    }
    process.exit(1);
  }
}

testConversationAPI().catch(console.error);
