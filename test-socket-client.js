const { io } = require("socket.io-client");

// Test configuration
const SERVER_URL = "http://localhost:3002";
const TEST_USER = {
  username: "testuser",
  email: "<EMAIL>",
  password: "Password123",
};

async function testSocketConnection() {
  console.log("🧪 Starting Socket.IO Manual Test...\n");

  try {
    // Step 1: Get authentication token
    console.log("1️⃣ Getting authentication token...");

    const authResponse = await fetch(`${SERVER_URL}/api/auth/login`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        email: TEST_USER.email,
        password: TEST_USER.password,
      }),
    });

    if (!authResponse.ok) {
      console.log("❌ Authentication failed. Creating test user...");

      // Try to register first
      const registerResponse = await fetch(`${SERVER_URL}/api/auth/register`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          username: TEST_USER.username,
          email: TEST_USER.email,
          password: TEST_USER.password,
        }),
      });

      if (!registerResponse.ok) {
        const registerError = await registerResponse.text();
        console.log("❌ Registration failed:", registerError);
        return;
      }

      console.log("✅ Test user created successfully");

      // Try login again
      const loginResponse = await fetch(`${SERVER_URL}/api/auth/login`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          email: TEST_USER.email,
          password: TEST_USER.password,
        }),
      });

      if (!loginResponse.ok) {
        const loginError = await loginResponse.text();
        console.log("❌ Login failed after registration:", loginError);
        return;
      }

      const loginData = await loginResponse.json();
      console.log("   Login response:", JSON.stringify(loginData, null, 2));
      var token =
        loginData.data?.accessToken || loginData.data?.token || loginData.token;
    } else {
      const authData = await authResponse.json();
      console.log("   Auth response:", JSON.stringify(authData, null, 2));
      var token =
        authData.data?.accessToken || authData.data?.token || authData.token;
    }

    console.log("✅ Authentication token obtained");
    if (token) {
      console.log("   Token preview:", token.substring(0, 20) + "...");
    } else {
      console.log("   ❌ Token is undefined!");
      return;
    }

    // Step 2: Test Socket.IO connection without token (should fail)
    console.log("\n2️⃣ Testing connection without token (should fail)...");

    const socketWithoutAuth = io(SERVER_URL, {
      autoConnect: false,
    });

    socketWithoutAuth.on("connect_error", (error) => {
      console.log("✅ Expected error without token:", error.message);
      socketWithoutAuth.disconnect();
    });

    socketWithoutAuth.connect();

    // Wait a bit for the error
    await new Promise((resolve) => setTimeout(resolve, 2000));

    // Step 3: Test Socket.IO connection with token (should succeed)
    console.log("\n3️⃣ Testing connection with valid token...");
    console.log("   Using token:", token.substring(0, 20) + "...");

    const socket = io(SERVER_URL, {
      auth: {
        token: token,
      },
      autoConnect: false,
    });

    // Set up event listeners
    socket.on("connect", () => {
      console.log("✅ Socket connected successfully!");
      console.log("   Socket ID:", socket.id);
    });

    socket.on("connect_error", (error) => {
      console.log("❌ Connection error:", error.message);
    });

    socket.on("user:online", (data) => {
      console.log("📡 User online event:", data);
    });

    socket.on("user:offline", (data) => {
      console.log("📡 User offline event:", data);
    });

    socket.on("error", (data) => {
      console.log("❌ Socket error:", data);
    });

    // Connect
    socket.connect();

    // Wait for connection
    await new Promise((resolve) => setTimeout(resolve, 3000));

    // Step 4: Test joining a conversation (should fail - no conversation)
    console.log(
      "\n4️⃣ Testing join conversation (should fail - invalid conversation)..."
    );

    socket.emit("join:conversation", {
      conversationId: "invalid-conversation-id",
    });

    // Wait for error
    await new Promise((resolve) => setTimeout(resolve, 2000));

    // Step 5: Test typing events
    console.log("\n5️⃣ Testing typing events...");

    socket.emit("typing:start", {
      conversationId: "test-conversation-id",
    });

    await new Promise((resolve) => setTimeout(resolve, 1000));

    socket.emit("typing:stop", {
      conversationId: "test-conversation-id",
    });

    // Step 6: Disconnect
    console.log("\n6️⃣ Disconnecting...");
    socket.disconnect();

    console.log("\n🎉 Socket.IO Manual Test Completed!");
  } catch (error) {
    console.error("❌ Test failed:", error.message);
  }
}

// Run the test
testSocketConnection();
