{
  message: 'Sentry DSN not configured, error tracking disabled',
  level: 'warn',
  timestamp: '2025-08-05 00:03:59'
}
{
  message: 'Sentry DSN not configured, error tracking disabled',
  level: 'warn',
  timestamp: '2025-08-05 00:04:11'
}
{
  message: 'Sentry DSN not configured, error tracking disabled',
  level: 'warn',
  timestamp: '2025-08-05 00:04:30'
}
{
  message: 'Sentry DSN not configured, error tracking disabled',
  level: 'warn',
  timestamp: '2025-08-05 00:04:56'
}
{
  message: 'Sentry DSN not configured, error tracking disabled',
  level: 'warn',
  timestamp: '2025-08-05 00:05:09'
}
{
  message: 'Sentry DSN not configured, error tracking disabled',
  level: 'warn',
  timestamp: '2025-08-05 00:05:23'
}
{
  port: '3000',
  environment: 'development',
  healthCheck: 'http://localhost:3000/health',
  authAPI: 'http://localhost:3000/api/auth',
  level: 'info',
  message: '🚀 Server running on port 3000',
  timestamp: '2025-08-05 00:05:23'
}
{
  message: 'Sentry DSN not configured, error tracking disabled',
  level: 'warn',
  timestamp: '2025-08-05 00:05:37'
}
{
  port: '3000',
  environment: 'development',
  healthCheck: 'http://localhost:3000/health',
  authAPI: 'http://localhost:3000/api/auth',
  level: 'info',
  message: '🚀 Server running on port 3000',
  timestamp: '2025-08-05 00:05:37'
}
{
  signal: 'SIGTERM',
  level: 'info',
  message: 'SIGTERM received. Starting graceful shutdown...',
  timestamp: '2025-08-05 00:05:48'
}
{
  message: 'Sentry DSN not configured, error tracking disabled',
  level: 'warn',
  timestamp: '2025-08-05 00:05:48'
}
{
  port: '3000',
  environment: 'development',
  healthCheck: 'http://localhost:3000/health',
  authAPI: 'http://localhost:3000/api/auth',
  level: 'info',
  message: '🚀 Server running on port 3000',
  timestamp: '2025-08-05 00:05:48'
}
{
  signal: 'SIGTERM',
  level: 'info',
  message: 'SIGTERM received. Starting graceful shutdown...',
  timestamp: '2025-08-05 00:06:14'
}
{
  message: 'Sentry DSN not configured, error tracking disabled',
  level: 'warn',
  timestamp: '2025-08-05 00:06:15'
}
{
  port: '3000',
  environment: 'development',
  healthCheck: 'http://localhost:3000/health',
  authAPI: 'http://localhost:3000/api/auth',
  level: 'info',
  message: '🚀 Server running on port 3000',
  timestamp: '2025-08-05 00:06:15'
}
{
  signal: 'SIGTERM',
  level: 'info',
  message: 'SIGTERM received. Starting graceful shutdown...',
  timestamp: '2025-08-05 00:06:31'
}
{
  message: 'Sentry DSN not configured, error tracking disabled',
  level: 'warn',
  timestamp: '2025-08-05 00:06:32'
}
{
  port: '3000',
  environment: 'development',
  healthCheck: 'http://localhost:3000/health',
  authAPI: 'http://localhost:3000/api/auth',
  level: 'info',
  message: '🚀 Server running on port 3000',
  timestamp: '2025-08-05 00:06:32'
}
{
  signal: 'SIGTERM',
  level: 'info',
  message: 'SIGTERM received. Starting graceful shutdown...',
  timestamp: '2025-08-05 00:06:40'
}
{
  message: 'Sentry DSN not configured, error tracking disabled',
  level: 'warn',
  timestamp: '2025-08-05 00:06:41'
}
{
  port: '3000',
  environment: 'development',
  healthCheck: 'http://localhost:3000/health',
  authAPI: 'http://localhost:3000/api/auth',
  level: 'info',
  message: '🚀 Server running on port 3000',
  timestamp: '2025-08-05 00:06:41'
}
{
  signal: 'SIGTERM',
  level: 'info',
  message: 'SIGTERM received. Starting graceful shutdown...',
  timestamp: '2025-08-05 00:06:59'
}
{
  message: 'Sentry DSN not configured, error tracking disabled',
  level: 'warn',
  timestamp: '2025-08-05 00:06:59'
}
{
  port: '3000',
  environment: 'development',
  healthCheck: 'http://localhost:3000/health',
  authAPI: 'http://localhost:3000/api/auth',
  level: 'info',
  message: '🚀 Server running on port 3000',
  timestamp: '2025-08-05 00:06:59'
}
{
  signal: 'SIGTERM',
  level: 'info',
  message: 'SIGTERM received. Starting graceful shutdown...',
  timestamp: '2025-08-05 00:07:11'
}
{
  message: 'Sentry DSN not configured, error tracking disabled',
  level: 'warn',
  timestamp: '2025-08-05 00:07:11'
}
{
  port: '3000',
  environment: 'development',
  healthCheck: 'http://localhost:3000/health',
  authAPI: 'http://localhost:3000/api/auth',
  level: 'info',
  message: '🚀 Server running on port 3000',
  timestamp: '2025-08-05 00:07:11'
}
{
  signal: 'SIGTERM',
  level: 'info',
  message: 'SIGTERM received. Starting graceful shutdown...',
  timestamp: '2025-08-05 00:07:31'
}
{
  message: 'Sentry DSN not configured, error tracking disabled',
  level: 'warn',
  timestamp: '2025-08-05 00:07:32'
}
{
  port: '3000',
  environment: 'development',
  healthCheck: 'http://localhost:3000/health',
  authAPI: 'http://localhost:3000/api/auth',
  level: 'info',
  message: '🚀 Server running on port 3000',
  timestamp: '2025-08-05 00:07:32'
}
{
  signal: 'SIGTERM',
  level: 'info',
  message: 'SIGTERM received. Starting graceful shutdown...',
  timestamp: '2025-08-05 00:07:50'
}
{
  message: 'Sentry DSN not configured, error tracking disabled',
  level: 'warn',
  timestamp: '2025-08-05 00:07:51'
}
{
  port: '3000',
  environment: 'development',
  healthCheck: 'http://localhost:3000/health',
  authAPI: 'http://localhost:3000/api/auth',
  level: 'info',
  message: '🚀 Server running on port 3000',
  timestamp: '2025-08-05 00:07:51'
}
{
  signal: 'SIGTERM',
  level: 'info',
  message: 'SIGTERM received. Starting graceful shutdown...',
  timestamp: '2025-08-05 00:08:03'
}
{
  message: 'Sentry DSN not configured, error tracking disabled',
  level: 'warn',
  timestamp: '2025-08-05 00:08:03'
}
{
  port: '3000',
  environment: 'development',
  healthCheck: 'http://localhost:3000/health',
  authAPI: 'http://localhost:3000/api/auth',
  level: 'info',
  message: '🚀 Server running on port 3000',
  timestamp: '2025-08-05 00:08:03'
}
{
  signal: 'SIGTERM',
  level: 'info',
  message: 'SIGTERM received. Starting graceful shutdown...',
  timestamp: '2025-08-05 00:08:25'
}
{
  message: 'Sentry DSN not configured, error tracking disabled',
  level: 'warn',
  timestamp: '2025-08-05 00:08:25'
}
{
  port: '3000',
  environment: 'development',
  healthCheck: 'http://localhost:3000/health',
  authAPI: 'http://localhost:3000/api/auth',
  level: 'info',
  message: '🚀 Server running on port 3000',
  timestamp: '2025-08-05 00:08:25'
}
{
  signal: 'SIGTERM',
  level: 'info',
  message: 'SIGTERM received. Starting graceful shutdown...',
  timestamp: '2025-08-05 00:08:37'
}
{
  message: 'Sentry DSN not configured, error tracking disabled',
  level: 'warn',
  timestamp: '2025-08-05 00:08:37'
}
{
  port: '3000',
  environment: 'development',
  healthCheck: 'http://localhost:3000/health',
  authAPI: 'http://localhost:3000/api/auth',
  level: 'info',
  message: '🚀 Server running on port 3000',
  timestamp: '2025-08-05 00:08:37'
}
{
  signal: 'SIGTERM',
  level: 'info',
  message: 'SIGTERM received. Starting graceful shutdown...',
  timestamp: '2025-08-05 00:08:48'
}
{
  message: 'Sentry DSN not configured, error tracking disabled',
  level: 'warn',
  timestamp: '2025-08-05 00:08:48'
}
{
  port: '3000',
  environment: 'development',
  healthCheck: 'http://localhost:3000/health',
  authAPI: 'http://localhost:3000/api/auth',
  level: 'info',
  message: '🚀 Server running on port 3000',
  timestamp: '2025-08-05 00:08:48'
}
{
  signal: 'SIGTERM',
  level: 'info',
  message: 'SIGTERM received. Starting graceful shutdown...',
  timestamp: '2025-08-05 00:09:00'
}
{
  message: 'Sentry DSN not configured, error tracking disabled',
  level: 'warn',
  timestamp: '2025-08-05 00:09:00'
}
{
  port: '3000',
  environment: 'development',
  healthCheck: 'http://localhost:3000/health',
  authAPI: 'http://localhost:3000/api/auth',
  level: 'info',
  message: '🚀 Server running on port 3000',
  timestamp: '2025-08-05 00:09:00'
}
{
  signal: 'SIGTERM',
  level: 'info',
  message: 'SIGTERM received. Starting graceful shutdown...',
  timestamp: '2025-08-05 00:09:20'
}
{
  message: 'Sentry DSN not configured, error tracking disabled',
  level: 'warn',
  timestamp: '2025-08-05 00:09:21'
}
{
  port: '3000',
  environment: 'development',
  healthCheck: 'http://localhost:3000/health',
  authAPI: 'http://localhost:3000/api/auth',
  level: 'info',
  message: '🚀 Server running on port 3000',
  timestamp: '2025-08-05 00:09:21'
}
{
  signal: 'SIGTERM',
  level: 'info',
  message: 'SIGTERM received. Starting graceful shutdown...',
  timestamp: '2025-08-05 00:09:33'
}
{
  message: 'Sentry DSN not configured, error tracking disabled',
  level: 'warn',
  timestamp: '2025-08-05 00:09:33'
}
{
  port: '3000',
  environment: 'development',
  healthCheck: 'http://localhost:3000/health',
  authAPI: 'http://localhost:3000/api/auth',
  level: 'info',
  message: '🚀 Server running on port 3000',
  timestamp: '2025-08-05 00:09:33'
}
{
  signal: 'SIGTERM',
  level: 'info',
  message: 'SIGTERM received. Starting graceful shutdown...',
  timestamp: '2025-08-05 00:09:46'
}
{
  message: 'Sentry DSN not configured, error tracking disabled',
  level: 'warn',
  timestamp: '2025-08-05 00:09:47'
}
{
  port: '3000',
  environment: 'development',
  healthCheck: 'http://localhost:3000/health',
  authAPI: 'http://localhost:3000/api/auth',
  level: 'info',
  message: '🚀 Server running on port 3000',
  timestamp: '2025-08-05 00:09:47'
}
{
  signal: 'SIGTERM',
  level: 'info',
  message: 'SIGTERM received. Starting graceful shutdown...',
  timestamp: '2025-08-05 00:17:36'
}
{
  message: 'Sentry DSN not configured, error tracking disabled',
  level: 'warn',
  timestamp: '2025-08-05 00:17:36'
}
{
  port: '3000',
  environment: 'development',
  healthCheck: 'http://localhost:3000/health',
  authAPI: 'http://localhost:3000/api/auth',
  level: 'info',
  message: '🚀 Server running on port 3000',
  timestamp: '2025-08-05 00:17:36'
}
{
  signal: 'SIGTERM',
  level: 'info',
  message: 'SIGTERM received. Starting graceful shutdown...',
  timestamp: '2025-08-05 00:18:06'
}
{
  message: 'Sentry DSN not configured, error tracking disabled',
  level: 'warn',
  timestamp: '2025-08-05 00:18:07'
}
{
  cors: {
    origin: 'http://localhost:3000',
    methods: [ 'GET', 'POST' ],
    credentials: true
  },
  pingTimeout: 60000,
  pingInterval: 25000,
  level: 'info',
  message: 'Socket.IO server created',
  timestamp: '2025-08-05 00:18:07'
}
{
  port: '3000',
  environment: 'development',
  healthCheck: 'http://localhost:3000/health',
  authAPI: 'http://localhost:3000/api/auth',
  socketIO: 'Socket.IO server initialized',
  level: 'info',
  message: '🚀 Server running on port 3000',
  timestamp: '2025-08-05 00:18:07'
}
{
  signal: 'SIGTERM',
  level: 'info',
  message: 'SIGTERM received. Starting graceful shutdown...',
  timestamp: '2025-08-05 00:19:44'
}
{
  message: 'Sentry DSN not configured, error tracking disabled',
  level: 'warn',
  timestamp: '2025-08-05 00:19:45'
}
{
  cors: {
    origin: 'http://localhost:3000',
    methods: [ 'GET', 'POST' ],
    credentials: true
  },
  pingTimeout: 60000,
  pingInterval: 25000,
  level: 'info',
  message: 'Socket.IO server created',
  timestamp: '2025-08-05 00:19:45'
}
{
  port: '3000',
  environment: 'development',
  healthCheck: 'http://localhost:3000/health',
  authAPI: 'http://localhost:3000/api/auth',
  socketIO: 'Socket.IO server initialized',
  level: 'info',
  message: '🚀 Server running on port 3000',
  timestamp: '2025-08-05 00:19:45'
}
{
  signal: 'SIGTERM',
  level: 'info',
  message: 'SIGTERM received. Starting graceful shutdown...',
  timestamp: '2025-08-05 00:19:57'
}
{
  message: 'Sentry DSN not configured, error tracking disabled',
  level: 'warn',
  timestamp: '2025-08-05 00:19:58'
}
{
  cors: {
    origin: 'http://localhost:3000',
    methods: [ 'GET', 'POST' ],
    credentials: true
  },
  pingTimeout: 60000,
  pingInterval: 25000,
  level: 'info',
  message: 'Socket.IO server created',
  timestamp: '2025-08-05 00:19:58'
}
{
  port: '3000',
  environment: 'development',
  healthCheck: 'http://localhost:3000/health',
  authAPI: 'http://localhost:3000/api/auth',
  socketIO: 'Socket.IO server initialized',
  level: 'info',
  message: '🚀 Server running on port 3000',
  timestamp: '2025-08-05 00:19:58'
}
{
  signal: 'SIGTERM',
  level: 'info',
  message: 'SIGTERM received. Starting graceful shutdown...',
  timestamp: '2025-08-05 00:20:09'
}
{
  message: 'Sentry DSN not configured, error tracking disabled',
  level: 'warn',
  timestamp: '2025-08-05 00:20:09'
}
{
  cors: {
    origin: 'http://localhost:3000',
    methods: [ 'GET', 'POST' ],
    credentials: true
  },
  pingTimeout: 60000,
  pingInterval: 25000,
  level: 'info',
  message: 'Socket.IO server created',
  timestamp: '2025-08-05 00:20:09'
}
{
  port: '3000',
  environment: 'development',
  healthCheck: 'http://localhost:3000/health',
  authAPI: 'http://localhost:3000/api/auth',
  socketIO: 'Socket.IO server initialized',
  level: 'info',
  message: '🚀 Server running on port 3000',
  timestamp: '2025-08-05 00:20:09'
}
{
  signal: 'SIGTERM',
  level: 'info',
  message: 'SIGTERM received. Starting graceful shutdown...',
  timestamp: '2025-08-05 00:20:20'
}
{
  message: 'Sentry DSN not configured, error tracking disabled',
  level: 'warn',
  timestamp: '2025-08-05 00:20:20'
}
{
  cors: {
    origin: 'http://localhost:3000',
    methods: [ 'GET', 'POST' ],
    credentials: true
  },
  pingTimeout: 60000,
  pingInterval: 25000,
  level: 'info',
  message: 'Socket.IO server created',
  timestamp: '2025-08-05 00:20:20'
}
{
  port: '3000',
  environment: 'development',
  healthCheck: 'http://localhost:3000/health',
  authAPI: 'http://localhost:3000/api/auth',
  socketIO: 'Socket.IO server initialized',
  level: 'info',
  message: '🚀 Server running on port 3000',
  timestamp: '2025-08-05 00:20:20'
}
{
  signal: 'SIGTERM',
  level: 'info',
  message: 'SIGTERM received. Starting graceful shutdown...',
  timestamp: '2025-08-05 00:30:17'
}
{
  message: 'Sentry DSN not configured, error tracking disabled',
  level: 'warn',
  timestamp: '2025-08-05 00:30:18'
}
{
  cors: {
    origin: 'http://localhost:3000',
    methods: [ 'GET', 'POST' ],
    credentials: true
  },
  pingTimeout: 60000,
  pingInterval: 25000,
  level: 'info',
  message: 'Socket.IO server created',
  timestamp: '2025-08-05 00:30:18'
}
{
  port: '3000',
  environment: 'development',
  healthCheck: 'http://localhost:3000/health',
  authAPI: 'http://localhost:3000/api/auth',
  socketIO: 'Socket.IO server initialized',
  level: 'info',
  message: '🚀 Server running on port 3000',
  timestamp: '2025-08-05 00:30:18'
}
{
  signal: 'SIGTERM',
  level: 'info',
  message: 'SIGTERM received. Starting graceful shutdown...',
  timestamp: '2025-08-05 00:31:21'
}
{
  message: 'Sentry DSN not configured, error tracking disabled',
  level: 'warn',
  timestamp: '2025-08-05 00:31:22'
}
{
  cors: {
    origin: 'http://localhost:3000',
    methods: [ 'GET', 'POST' ],
    credentials: true
  },
  pingTimeout: 60000,
  pingInterval: 25000,
  level: 'info',
  message: 'Socket.IO server created',
  timestamp: '2025-08-05 00:31:22'
}
{
  port: '3000',
  environment: 'development',
  healthCheck: 'http://localhost:3000/health',
  authAPI: 'http://localhost:3000/api/auth',
  socketIO: 'Socket.IO server initialized',
  level: 'info',
  message: '🚀 Server running on port 3000',
  timestamp: '2025-08-05 00:31:22'
}
{
  signal: 'SIGTERM',
  level: 'info',
  message: 'SIGTERM received. Starting graceful shutdown...',
  timestamp: '2025-08-05 00:31:34'
}
{
  message: 'Sentry DSN not configured, error tracking disabled',
  level: 'warn',
  timestamp: '2025-08-05 00:31:35'
}
{
  message: 'Sentry DSN not configured, error tracking disabled',
  level: 'warn',
  timestamp: '2025-08-05 00:31:48'
}
{
  cors: {
    origin: 'http://localhost:3000',
    methods: [ 'GET', 'POST' ],
    credentials: true
  },
  pingTimeout: 60000,
  pingInterval: 25000,
  level: 'info',
  message: 'Socket.IO server created',
  timestamp: '2025-08-05 00:31:48'
}
{
  port: '3000',
  environment: 'development',
  healthCheck: 'http://localhost:3000/health',
  authAPI: 'http://localhost:3000/api/auth',
  socketIO: 'Socket.IO server initialized',
  level: 'info',
  message: '🚀 Server running on port 3000',
  timestamp: '2025-08-05 00:31:48'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:31:48'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:31:48'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:31:48'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:31:48'
}
{
  error: 'Connection is closed.',
  stack: 'Error: Connection is closed.\n' +
    '    at EventEmitter.connectionCloseHandler (/Users/<USER>/Desktop/njback/node_modules/ioredis/built/Redis.js:208:28)\n' +
    '    at Object.onceWrapper (node:events:622:26)\n' +
    '    at EventEmitter.emit (node:events:519:35)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:85:11)',
  level: 'error',
  message: 'Failed to connect to Redis',
  timestamp: '2025-08-05 00:31:48'
}
{
  error: 'Connection is closed.',
  stack: 'Error: Connection is closed.\n' +
    '    at EventEmitter.connectionCloseHandler (/Users/<USER>/Desktop/njback/node_modules/ioredis/built/Redis.js:208:28)\n' +
    '    at Object.onceWrapper (node:events:622:26)\n' +
    '    at EventEmitter.emit (node:events:519:35)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:85:11)',
  context: undefined,
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:31:48'
}
{
  error: 'Connection is closed.',
  stack: 'Error: Connection is closed.\n' +
    '    at EventEmitter.connectionCloseHandler (/Users/<USER>/Desktop/njback/node_modules/ioredis/built/Redis.js:208:28)\n' +
    '    at Object.onceWrapper (node:events:622:26)\n' +
    '    at EventEmitter.emit (node:events:519:35)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:85:11)',
  level: 'error',
  message: 'Failed to connect Redis for Socket.IO service',
  timestamp: '2025-08-05 00:31:48'
}
{
  error: 'Connection is closed.',
  stack: 'Error: Connection is closed.\n' +
    '    at EventEmitter.connectionCloseHandler (/Users/<USER>/Desktop/njback/node_modules/ioredis/built/Redis.js:208:28)\n' +
    '    at Object.onceWrapper (node:events:622:26)\n' +
    '    at EventEmitter.emit (node:events:519:35)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:85:11)',
  context: undefined,
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:31:48'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:31:49'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:31:49'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:31:49'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:31:49'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:31:49'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:31:49'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:31:49'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:31:49'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:31:49'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:31:49'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:31:49'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:31:49'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:31:49'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:31:49'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:31:49'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:31:49'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:31:49'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:31:49'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:31:49'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:31:49'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:31:50'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:31:50'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:31:50'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:31:50'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:31:50'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:31:50'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:31:50'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:31:50'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:31:50'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:31:50'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:31:50'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:31:50'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:31:51'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:31:51'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:31:51'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:31:51'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:31:51'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:31:51'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:31:51'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:31:51'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:31:52'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:31:52'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:31:52'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:31:52'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:31:52'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:31:52'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:31:52'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:31:52'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:31:53'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:31:53'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:31:53'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:31:53'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:31:54'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:31:54'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:31:54'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:31:54'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:31:55'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:31:55'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:31:55'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:31:55'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:31:55'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:31:55'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:31:55'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:31:55'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:31:56'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:31:56'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:31:56'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:31:56'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:31:57'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:31:57'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:31:57'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:31:57'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:31:58'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:31:58'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:31:58'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:31:58'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:31:59'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:31:59'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:31:59'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:31:59'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:00'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:00'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:32:00'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:32:00'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:01'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:01'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:32:01'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:32:01'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:02'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:02'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:32:02'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:32:02'
}
{
  signal: 'SIGTERM',
  level: 'info',
  message: 'SIGTERM received. Starting graceful shutdown...',
  timestamp: '2025-08-05 00:32:03'
}
{
  message: 'Sentry DSN not configured, error tracking disabled',
  level: 'warn',
  timestamp: '2025-08-05 00:32:04'
}
{
  cors: {
    origin: 'http://localhost:3000',
    methods: [ 'GET', 'POST' ],
    credentials: true
  },
  pingTimeout: 60000,
  pingInterval: 25000,
  level: 'info',
  message: 'Socket.IO server created',
  timestamp: '2025-08-05 00:32:04'
}
{
  port: '3000',
  environment: 'development',
  healthCheck: 'http://localhost:3000/health',
  authAPI: 'http://localhost:3000/api/auth',
  socketIO: 'Socket.IO server initialized',
  level: 'info',
  message: '🚀 Server running on port 3000',
  timestamp: '2025-08-05 00:32:04'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:04'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:04'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:32:04'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:32:04'
}
{
  error: 'Connection is closed.',
  stack: 'Error: Connection is closed.\n' +
    '    at EventEmitter.connectionCloseHandler (/Users/<USER>/Desktop/njback/node_modules/ioredis/built/Redis.js:208:28)\n' +
    '    at Object.onceWrapper (node:events:622:26)\n' +
    '    at EventEmitter.emit (node:events:519:35)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:85:11)',
  level: 'error',
  message: 'Failed to connect to Redis',
  timestamp: '2025-08-05 00:32:04'
}
{
  error: 'Connection is closed.',
  stack: 'Error: Connection is closed.\n' +
    '    at EventEmitter.connectionCloseHandler (/Users/<USER>/Desktop/njback/node_modules/ioredis/built/Redis.js:208:28)\n' +
    '    at Object.onceWrapper (node:events:622:26)\n' +
    '    at EventEmitter.emit (node:events:519:35)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:85:11)',
  context: undefined,
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:04'
}
{
  error: 'Connection is closed.',
  stack: 'Error: Connection is closed.\n' +
    '    at EventEmitter.connectionCloseHandler (/Users/<USER>/Desktop/njback/node_modules/ioredis/built/Redis.js:208:28)\n' +
    '    at Object.onceWrapper (node:events:622:26)\n' +
    '    at EventEmitter.emit (node:events:519:35)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:85:11)',
  level: 'error',
  message: 'Failed to connect Redis for Socket.IO service',
  timestamp: '2025-08-05 00:32:04'
}
{
  error: 'Connection is closed.',
  stack: 'Error: Connection is closed.\n' +
    '    at EventEmitter.connectionCloseHandler (/Users/<USER>/Desktop/njback/node_modules/ioredis/built/Redis.js:208:28)\n' +
    '    at Object.onceWrapper (node:events:622:26)\n' +
    '    at EventEmitter.emit (node:events:519:35)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:85:11)',
  context: undefined,
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:04'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:04'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:04'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:32:04'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:32:04'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:04'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:04'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:32:04'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:32:04'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:04'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:04'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:32:04'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:32:04'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:04'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:04'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:32:04'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:32:04'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:04'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:04'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:32:04'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:32:04'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:05'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:05'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:32:05'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:32:05'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:05'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:05'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:32:05'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:32:05'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:05'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:05'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:32:05'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:32:05'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:06'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:06'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:32:06'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:32:06'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:06'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:06'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:32:06'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:32:06'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:07'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:07'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:32:07'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:32:07'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:08'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:08'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:32:08'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:32:08'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:08'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:08'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:32:08'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:32:08'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:09'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:09'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:32:09'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:32:09'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:10'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:10'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:32:10'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:32:10'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:11'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:11'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:32:11'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:32:11'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:11'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:11'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:32:11'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:32:11'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:12'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:12'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:32:12'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:32:12'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:13'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:13'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:32:13'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:32:13'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:14'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:14'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:32:14'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:32:14'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:15'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:15'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:32:15'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:32:15'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:16'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:16'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:32:16'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:32:16'
}
{
  signal: 'SIGTERM',
  level: 'info',
  message: 'SIGTERM received. Starting graceful shutdown...',
  timestamp: '2025-08-05 00:32:17'
}
{
  message: 'Sentry DSN not configured, error tracking disabled',
  level: 'warn',
  timestamp: '2025-08-05 00:32:17'
}
{
  cors: {
    origin: 'http://localhost:3000',
    methods: [ 'GET', 'POST' ],
    credentials: true
  },
  pingTimeout: 60000,
  pingInterval: 25000,
  level: 'info',
  message: 'Socket.IO server created',
  timestamp: '2025-08-05 00:32:17'
}
{
  port: '3000',
  environment: 'development',
  healthCheck: 'http://localhost:3000/health',
  authAPI: 'http://localhost:3000/api/auth',
  socketIO: 'Socket.IO server initialized',
  level: 'info',
  message: '🚀 Server running on port 3000',
  timestamp: '2025-08-05 00:32:17'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:17'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:17'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:32:17'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:32:17'
}
{
  error: 'Connection is closed.',
  stack: 'Error: Connection is closed.\n' +
    '    at EventEmitter.connectionCloseHandler (/Users/<USER>/Desktop/njback/node_modules/ioredis/built/Redis.js:208:28)\n' +
    '    at Object.onceWrapper (node:events:622:26)\n' +
    '    at EventEmitter.emit (node:events:519:35)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:85:11)',
  level: 'error',
  message: 'Failed to connect to Redis',
  timestamp: '2025-08-05 00:32:17'
}
{
  error: 'Connection is closed.',
  stack: 'Error: Connection is closed.\n' +
    '    at EventEmitter.connectionCloseHandler (/Users/<USER>/Desktop/njback/node_modules/ioredis/built/Redis.js:208:28)\n' +
    '    at Object.onceWrapper (node:events:622:26)\n' +
    '    at EventEmitter.emit (node:events:519:35)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:85:11)',
  context: undefined,
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:17'
}
{
  error: 'Connection is closed.',
  stack: 'Error: Connection is closed.\n' +
    '    at EventEmitter.connectionCloseHandler (/Users/<USER>/Desktop/njback/node_modules/ioredis/built/Redis.js:208:28)\n' +
    '    at Object.onceWrapper (node:events:622:26)\n' +
    '    at EventEmitter.emit (node:events:519:35)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:85:11)',
  level: 'error',
  message: 'Failed to connect Redis for Socket.IO service',
  timestamp: '2025-08-05 00:32:17'
}
{
  error: 'Connection is closed.',
  stack: 'Error: Connection is closed.\n' +
    '    at EventEmitter.connectionCloseHandler (/Users/<USER>/Desktop/njback/node_modules/ioredis/built/Redis.js:208:28)\n' +
    '    at Object.onceWrapper (node:events:622:26)\n' +
    '    at EventEmitter.emit (node:events:519:35)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:85:11)',
  context: undefined,
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:17'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:17'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:17'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:32:17'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:32:17'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:17'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:17'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:32:17'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:32:17'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:17'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:17'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:32:17'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:32:17'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:18'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:18'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:32:18'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:32:18'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:18'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:18'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:32:18'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:32:18'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:18'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:18'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:32:18'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:32:18'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:19'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:19'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:32:19'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:32:19'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:19'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:19'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:32:19'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:32:19'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:19'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:19'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:32:19'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:32:19'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:20'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:20'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:32:20'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:32:20'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:20'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:20'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:32:20'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:32:20'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:21'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:21'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:32:21'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:32:21'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:22'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:22'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:32:22'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:32:22'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:22'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:22'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:32:22'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:32:22'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:23'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:23'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:32:23'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:32:23'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:24'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:24'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:32:24'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:32:24'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:25'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:25'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:32:25'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:32:25'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:26'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:26'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:32:26'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:32:26'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:27'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:27'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:32:27'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:32:27'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:28'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:28'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:32:28'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:32:28'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:29'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:29'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:32:29'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:32:29'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:30'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:30'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:32:30'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:32:30'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:31'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:31'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:32:31'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:32:31'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:32'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:32'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:32:32'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:32:32'
}
{
  signal: 'SIGTERM',
  level: 'info',
  message: 'SIGTERM received. Starting graceful shutdown...',
  timestamp: '2025-08-05 00:32:33'
}
{
  message: 'Sentry DSN not configured, error tracking disabled',
  level: 'warn',
  timestamp: '2025-08-05 00:32:33'
}
{
  cors: {
    origin: 'http://localhost:3000',
    methods: [ 'GET', 'POST' ],
    credentials: true
  },
  pingTimeout: 60000,
  pingInterval: 25000,
  level: 'info',
  message: 'Socket.IO server created',
  timestamp: '2025-08-05 00:32:33'
}
{
  port: '3000',
  environment: 'development',
  healthCheck: 'http://localhost:3000/health',
  authAPI: 'http://localhost:3000/api/auth',
  socketIO: 'Socket.IO server initialized',
  level: 'info',
  message: '🚀 Server running on port 3000',
  timestamp: '2025-08-05 00:32:33'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:33'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:33'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:32:33'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:32:33'
}
{
  error: 'Connection is closed.',
  stack: 'Error: Connection is closed.\n' +
    '    at EventEmitter.connectionCloseHandler (/Users/<USER>/Desktop/njback/node_modules/ioredis/built/Redis.js:208:28)\n' +
    '    at Object.onceWrapper (node:events:622:26)\n' +
    '    at EventEmitter.emit (node:events:519:35)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:85:11)',
  level: 'error',
  message: 'Failed to connect to Redis',
  timestamp: '2025-08-05 00:32:33'
}
{
  error: 'Connection is closed.',
  stack: 'Error: Connection is closed.\n' +
    '    at EventEmitter.connectionCloseHandler (/Users/<USER>/Desktop/njback/node_modules/ioredis/built/Redis.js:208:28)\n' +
    '    at Object.onceWrapper (node:events:622:26)\n' +
    '    at EventEmitter.emit (node:events:519:35)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:85:11)',
  context: undefined,
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:33'
}
{
  error: 'Connection is closed.',
  stack: 'Error: Connection is closed.\n' +
    '    at EventEmitter.connectionCloseHandler (/Users/<USER>/Desktop/njback/node_modules/ioredis/built/Redis.js:208:28)\n' +
    '    at Object.onceWrapper (node:events:622:26)\n' +
    '    at EventEmitter.emit (node:events:519:35)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:85:11)',
  level: 'error',
  message: 'Failed to connect Redis for Socket.IO service',
  timestamp: '2025-08-05 00:32:33'
}
{
  error: 'Connection is closed.',
  stack: 'Error: Connection is closed.\n' +
    '    at EventEmitter.connectionCloseHandler (/Users/<USER>/Desktop/njback/node_modules/ioredis/built/Redis.js:208:28)\n' +
    '    at Object.onceWrapper (node:events:622:26)\n' +
    '    at EventEmitter.emit (node:events:519:35)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:85:11)',
  context: undefined,
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:33'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:33'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:33'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:32:33'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:32:33'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:33'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:33'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:32:33'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:32:33'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:33'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:33'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:32:33'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:32:33'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:34'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:34'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:32:34'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:32:34'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:34'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:34'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:32:34'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:32:34'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:34'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:34'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:32:34'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:32:34'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:35'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:35'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:32:35'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:32:35'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:35'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:35'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:32:35'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:32:35'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:35'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:35'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:32:35'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:32:35'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:36'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:36'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:32:36'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:32:36'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:36'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:36'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:32:36'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:32:36'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:37'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:37'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:32:37'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:32:37'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:38'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:38'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:32:38'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:32:38'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:38'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:38'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:32:38'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:32:38'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:39'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:39'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:32:39'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:32:39'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:40'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:40'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:32:40'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:32:40'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:41'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:41'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:32:41'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:32:41'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:42'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:42'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:32:42'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:32:42'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:43'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:43'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:32:43'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:32:43'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:44'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:44'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:32:44'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:32:44'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:45'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:45'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:32:45'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:32:45'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:46'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:46'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:32:46'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:32:46'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:47'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:47'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:32:47'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:32:47'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:48'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:48'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:32:48'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:32:48'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:49'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:49'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:32:49'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:32:49'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:51'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:51'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:32:51'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:32:51'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:52'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:52'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:32:52'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:32:52'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:54'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:54'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:32:54'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:32:54'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:55'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:55'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:32:55'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:32:55'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:57'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:57'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:32:57'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:32:57'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:58'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:58'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:32:58'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:32:58'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:00'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:00'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:33:00'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:33:00'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:01'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:01'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:33:01'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:33:01'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:03'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:03'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:33:03'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:33:03'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:05'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:05'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:33:05'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:33:05'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:07'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:07'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:33:07'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:33:07'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:08'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:08'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:33:08'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:33:08'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:10'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:10'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:33:10'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:33:10'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:12'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:12'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:33:12'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:33:12'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:14'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:14'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:33:14'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:33:14'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:16'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:16'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:33:16'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:33:16'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:18'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:18'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:33:18'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:33:18'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:20'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:20'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:33:20'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:33:20'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:22'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:22'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:33:22'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:33:22'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:24'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:24'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:33:24'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:33:24'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:26'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:26'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:33:26'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:33:26'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:28'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:28'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:33:28'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:33:28'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:30'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:30'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:33:30'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:33:30'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:32'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:32'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:33:32'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:33:32'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:34'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:34'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:33:34'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:33:34'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:36'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:36'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:33:36'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:33:36'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:38'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:38'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:33:38'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:33:38'
}
{
  signal: 'SIGTERM',
  level: 'info',
  message: 'SIGTERM received. Starting graceful shutdown...',
  timestamp: '2025-08-05 00:33:39'
}
{
  message: 'Sentry DSN not configured, error tracking disabled',
  level: 'warn',
  timestamp: '2025-08-05 00:33:40'
}
{
  cors: {
    origin: 'http://localhost:3000',
    methods: [ 'GET', 'POST' ],
    credentials: true
  },
  pingTimeout: 60000,
  pingInterval: 25000,
  level: 'info',
  message: 'Socket.IO server created',
  timestamp: '2025-08-05 00:33:40'
}
{
  port: '3000',
  environment: 'development',
  healthCheck: 'http://localhost:3000/health',
  authAPI: 'http://localhost:3000/api/auth',
  socketIO: 'Socket.IO server initialized',
  level: 'info',
  message: '🚀 Server running on port 3000',
  timestamp: '2025-08-05 00:33:40'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:40'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:40'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:33:40'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:33:40'
}
{
  error: 'Connection is closed.',
  stack: 'Error: Connection is closed.\n' +
    '    at EventEmitter.connectionCloseHandler (/Users/<USER>/Desktop/njback/node_modules/ioredis/built/Redis.js:208:28)\n' +
    '    at Object.onceWrapper (node:events:622:26)\n' +
    '    at EventEmitter.emit (node:events:519:35)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:85:11)',
  level: 'error',
  message: 'Failed to connect to Redis',
  timestamp: '2025-08-05 00:33:40'
}
{
  error: 'Connection is closed.',
  stack: 'Error: Connection is closed.\n' +
    '    at EventEmitter.connectionCloseHandler (/Users/<USER>/Desktop/njback/node_modules/ioredis/built/Redis.js:208:28)\n' +
    '    at Object.onceWrapper (node:events:622:26)\n' +
    '    at EventEmitter.emit (node:events:519:35)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:85:11)',
  context: undefined,
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:40'
}
{
  error: 'Connection is closed.',
  stack: 'Error: Connection is closed.\n' +
    '    at EventEmitter.connectionCloseHandler (/Users/<USER>/Desktop/njback/node_modules/ioredis/built/Redis.js:208:28)\n' +
    '    at Object.onceWrapper (node:events:622:26)\n' +
    '    at EventEmitter.emit (node:events:519:35)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:85:11)',
  level: 'error',
  message: 'Failed to connect Redis for Socket.IO service',
  timestamp: '2025-08-05 00:33:40'
}
{
  error: 'Connection is closed.',
  stack: 'Error: Connection is closed.\n' +
    '    at EventEmitter.connectionCloseHandler (/Users/<USER>/Desktop/njback/node_modules/ioredis/built/Redis.js:208:28)\n' +
    '    at Object.onceWrapper (node:events:622:26)\n' +
    '    at EventEmitter.emit (node:events:519:35)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:85:11)',
  context: undefined,
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:40'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:40'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:40'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:33:40'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:33:40'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:40'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:40'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:33:40'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:33:40'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:40'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:40'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:33:40'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:33:40'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:40'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:40'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:33:40'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:33:40'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:40'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:40'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:33:40'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:33:40'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:41'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:41'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:33:41'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:33:41'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:41'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:41'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:33:41'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:33:41'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:41'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:41'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:33:41'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:33:41'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:42'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:42'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:33:42'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:33:42'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:42'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:42'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:33:42'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:33:42'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:43'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:43'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:33:43'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:33:43'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:44'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:44'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:33:44'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:33:44'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:44'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:44'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:33:44'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:33:44'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:45'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:45'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:33:45'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:33:45'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:46'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:46'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:33:46'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:33:46'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:46'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:46'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:33:46'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:33:46'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:47'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:47'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:33:47'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:33:47'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:48'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:48'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:33:48'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:33:48'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:49'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:49'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:33:49'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:33:49'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:50'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:50'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:33:50'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:33:50'
}
{
  signal: 'SIGTERM',
  level: 'info',
  message: 'SIGTERM received. Starting graceful shutdown...',
  timestamp: '2025-08-05 00:33:51'
}
{
  message: 'Sentry DSN not configured, error tracking disabled',
  level: 'warn',
  timestamp: '2025-08-05 00:33:51'
}
{
  cors: {
    origin: 'http://localhost:3000',
    methods: [ 'GET', 'POST' ],
    credentials: true
  },
  pingTimeout: 60000,
  pingInterval: 25000,
  level: 'info',
  message: 'Socket.IO server created',
  timestamp: '2025-08-05 00:33:51'
}
{
  port: '3000',
  environment: 'development',
  healthCheck: 'http://localhost:3000/health',
  authAPI: 'http://localhost:3000/api/auth',
  socketIO: 'Socket.IO server initialized',
  level: 'info',
  message: '🚀 Server running on port 3000',
  timestamp: '2025-08-05 00:33:51'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:51'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:51'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:33:51'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:33:51'
}
{
  error: 'Connection is closed.',
  stack: 'Error: Connection is closed.\n' +
    '    at EventEmitter.connectionCloseHandler (/Users/<USER>/Desktop/njback/node_modules/ioredis/built/Redis.js:208:28)\n' +
    '    at Object.onceWrapper (node:events:622:26)\n' +
    '    at EventEmitter.emit (node:events:519:35)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:85:11)',
  level: 'error',
  message: 'Failed to connect to Redis',
  timestamp: '2025-08-05 00:33:51'
}
{
  error: 'Connection is closed.',
  stack: 'Error: Connection is closed.\n' +
    '    at EventEmitter.connectionCloseHandler (/Users/<USER>/Desktop/njback/node_modules/ioredis/built/Redis.js:208:28)\n' +
    '    at Object.onceWrapper (node:events:622:26)\n' +
    '    at EventEmitter.emit (node:events:519:35)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:85:11)',
  context: undefined,
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:51'
}
{
  error: 'Connection is closed.',
  stack: 'Error: Connection is closed.\n' +
    '    at EventEmitter.connectionCloseHandler (/Users/<USER>/Desktop/njback/node_modules/ioredis/built/Redis.js:208:28)\n' +
    '    at Object.onceWrapper (node:events:622:26)\n' +
    '    at EventEmitter.emit (node:events:519:35)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:85:11)',
  level: 'error',
  message: 'Failed to connect Redis for Socket.IO service',
  timestamp: '2025-08-05 00:33:51'
}
{
  error: 'Connection is closed.',
  stack: 'Error: Connection is closed.\n' +
    '    at EventEmitter.connectionCloseHandler (/Users/<USER>/Desktop/njback/node_modules/ioredis/built/Redis.js:208:28)\n' +
    '    at Object.onceWrapper (node:events:622:26)\n' +
    '    at EventEmitter.emit (node:events:519:35)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:85:11)',
  context: undefined,
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:51'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:51'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:51'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:33:51'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:33:51'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:51'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:51'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:33:51'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:33:51'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:52'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:52'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:33:52'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:33:52'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:52'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:52'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:33:52'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:33:52'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:52'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:52'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:33:52'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:33:52'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:52'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:52'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:33:52'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:33:52'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:53'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:53'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:33:53'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:33:53'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:53'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:53'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:33:53'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:33:53'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:54'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:54'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:33:54'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:33:54'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:54'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:54'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:33:54'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:33:54'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:55'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:55'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:33:55'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:33:55'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:55'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:55'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:33:55'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:33:55'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:56'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:56'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:33:56'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:33:56'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:57'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:57'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:33:57'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:33:57'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:57'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:57'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:33:57'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:33:57'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:58'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:58'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:33:58'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:33:58'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:59'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:59'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:33:59'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:33:59'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:00'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:00'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:34:00'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:34:00'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:01'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:01'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:34:01'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:34:01'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:02'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:02'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:34:02'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:34:02'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:03'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:03'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:34:03'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:34:03'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:04'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:04'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:34:04'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:34:04'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:05'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:05'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:34:05'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:34:05'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:06'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:06'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:34:06'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:34:06'
}
{
  signal: 'SIGTERM',
  level: 'info',
  message: 'SIGTERM received. Starting graceful shutdown...',
  timestamp: '2025-08-05 00:34:06'
}
{
  message: 'Sentry DSN not configured, error tracking disabled',
  level: 'warn',
  timestamp: '2025-08-05 00:34:07'
}
{
  cors: {
    origin: 'http://localhost:3000',
    methods: [ 'GET', 'POST' ],
    credentials: true
  },
  pingTimeout: 60000,
  pingInterval: 25000,
  level: 'info',
  message: 'Socket.IO server created',
  timestamp: '2025-08-05 00:34:07'
}
{
  port: '3000',
  environment: 'development',
  healthCheck: 'http://localhost:3000/health',
  authAPI: 'http://localhost:3000/api/auth',
  socketIO: 'Socket.IO server initialized',
  level: 'info',
  message: '🚀 Server running on port 3000',
  timestamp: '2025-08-05 00:34:07'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:07'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:07'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:34:07'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:34:07'
}
{
  error: 'Connection is closed.',
  stack: 'Error: Connection is closed.\n' +
    '    at EventEmitter.connectionCloseHandler (/Users/<USER>/Desktop/njback/node_modules/ioredis/built/Redis.js:208:28)\n' +
    '    at Object.onceWrapper (node:events:622:26)\n' +
    '    at EventEmitter.emit (node:events:519:35)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:85:11)',
  level: 'error',
  message: 'Failed to connect to Redis',
  timestamp: '2025-08-05 00:34:07'
}
{
  error: 'Connection is closed.',
  stack: 'Error: Connection is closed.\n' +
    '    at EventEmitter.connectionCloseHandler (/Users/<USER>/Desktop/njback/node_modules/ioredis/built/Redis.js:208:28)\n' +
    '    at Object.onceWrapper (node:events:622:26)\n' +
    '    at EventEmitter.emit (node:events:519:35)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:85:11)',
  context: undefined,
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:07'
}
{
  error: 'Connection is closed.',
  stack: 'Error: Connection is closed.\n' +
    '    at EventEmitter.connectionCloseHandler (/Users/<USER>/Desktop/njback/node_modules/ioredis/built/Redis.js:208:28)\n' +
    '    at Object.onceWrapper (node:events:622:26)\n' +
    '    at EventEmitter.emit (node:events:519:35)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:85:11)',
  level: 'error',
  message: 'Failed to connect Redis for Socket.IO service',
  timestamp: '2025-08-05 00:34:07'
}
{
  error: 'Connection is closed.',
  stack: 'Error: Connection is closed.\n' +
    '    at EventEmitter.connectionCloseHandler (/Users/<USER>/Desktop/njback/node_modules/ioredis/built/Redis.js:208:28)\n' +
    '    at Object.onceWrapper (node:events:622:26)\n' +
    '    at EventEmitter.emit (node:events:519:35)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:85:11)',
  context: undefined,
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:07'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:07'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:07'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:34:07'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:34:07'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:07'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:07'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:34:07'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:34:07'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:07'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:07'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:34:07'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:34:07'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:08'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:08'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:34:08'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:34:08'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:08'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:08'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:34:08'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:34:08'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:08'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:08'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:34:08'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:34:08'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:08'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:08'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:34:08'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:34:08'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:09'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:09'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:34:09'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:34:09'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:09'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:09'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:34:09'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:34:09'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:10'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:10'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:34:10'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:34:10'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:10'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:10'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:34:10'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:34:10'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:11'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:11'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:34:11'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:34:11'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:12'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:12'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:34:12'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:34:12'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:12'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:12'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:34:12'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:34:12'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:13'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:13'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:34:13'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:34:13'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:14'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:14'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:34:14'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:34:14'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:15'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:15'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:34:15'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:34:15'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:16'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:16'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:34:16'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:34:16'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:17'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:17'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:34:17'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:34:17'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:18'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:18'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:34:18'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:34:18'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:19'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:19'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:34:19'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:34:19'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:20'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:20'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:34:20'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:34:20'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:21'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:21'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:34:21'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:34:21'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:22'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:22'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:34:22'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:34:22'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:23'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:23'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:34:23'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:34:23'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:25'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:25'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:34:25'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:34:25'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:26'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:26'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:34:26'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:34:26'
}
{
  signal: 'SIGTERM',
  level: 'info',
  message: 'SIGTERM received. Starting graceful shutdown...',
  timestamp: '2025-08-05 00:34:26'
}
{
  message: 'Sentry DSN not configured, error tracking disabled',
  level: 'warn',
  timestamp: '2025-08-05 00:34:27'
}
{
  cors: {
    origin: 'http://localhost:3000',
    methods: [ 'GET', 'POST' ],
    credentials: true
  },
  pingTimeout: 60000,
  pingInterval: 25000,
  level: 'info',
  message: 'Socket.IO server created',
  timestamp: '2025-08-05 00:34:27'
}
{
  port: '3000',
  environment: 'development',
  healthCheck: 'http://localhost:3000/health',
  authAPI: 'http://localhost:3000/api/auth',
  socketIO: 'Socket.IO server initialized',
  level: 'info',
  message: '🚀 Server running on port 3000',
  timestamp: '2025-08-05 00:34:27'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:27'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:27'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:34:27'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:34:27'
}
{
  error: 'Connection is closed.',
  stack: 'Error: Connection is closed.\n' +
    '    at EventEmitter.connectionCloseHandler (/Users/<USER>/Desktop/njback/node_modules/ioredis/built/Redis.js:208:28)\n' +
    '    at Object.onceWrapper (node:events:622:26)\n' +
    '    at EventEmitter.emit (node:events:519:35)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:85:11)',
  level: 'error',
  message: 'Failed to connect to Redis',
  timestamp: '2025-08-05 00:34:27'
}
{
  error: 'Connection is closed.',
  stack: 'Error: Connection is closed.\n' +
    '    at EventEmitter.connectionCloseHandler (/Users/<USER>/Desktop/njback/node_modules/ioredis/built/Redis.js:208:28)\n' +
    '    at Object.onceWrapper (node:events:622:26)\n' +
    '    at EventEmitter.emit (node:events:519:35)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:85:11)',
  context: undefined,
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:27'
}
{
  error: 'Connection is closed.',
  stack: 'Error: Connection is closed.\n' +
    '    at EventEmitter.connectionCloseHandler (/Users/<USER>/Desktop/njback/node_modules/ioredis/built/Redis.js:208:28)\n' +
    '    at Object.onceWrapper (node:events:622:26)\n' +
    '    at EventEmitter.emit (node:events:519:35)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:85:11)',
  level: 'error',
  message: 'Failed to connect Redis for Socket.IO service',
  timestamp: '2025-08-05 00:34:27'
}
{
  error: 'Connection is closed.',
  stack: 'Error: Connection is closed.\n' +
    '    at EventEmitter.connectionCloseHandler (/Users/<USER>/Desktop/njback/node_modules/ioredis/built/Redis.js:208:28)\n' +
    '    at Object.onceWrapper (node:events:622:26)\n' +
    '    at EventEmitter.emit (node:events:519:35)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:85:11)',
  context: undefined,
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:27'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:27'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:27'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:34:27'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:34:27'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:27'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:27'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:34:27'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:34:27'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:27'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:27'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:34:27'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:34:27'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:27'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:27'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:34:27'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:34:27'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:28'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:28'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:34:28'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:34:28'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:28'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:28'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:34:28'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:34:28'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:28'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:28'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:34:28'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:34:28'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:29'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:29'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:34:29'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:34:29'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:29'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:29'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:34:29'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:34:29'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:30'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:30'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:34:30'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:34:30'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:30'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:30'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:34:30'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:34:30'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:31'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:31'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:34:31'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:34:31'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:31'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:31'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:34:31'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:34:31'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:32'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:32'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:34:32'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:34:32'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:33'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:33'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:34:33'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:34:33'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:34'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:34'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:34:34'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:34:34'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:34'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:34'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:34:34'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:34:34'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:35'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:35'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:34:35'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:34:35'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:36'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:36'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:34:36'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:34:36'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:37'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:37'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:34:37'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:34:37'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:38'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:38'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:34:38'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:34:38'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:40'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:40'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:34:40'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:34:40'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:41'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:41'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:34:41'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:34:41'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:42'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:42'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:34:42'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:34:42'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:43'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:43'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:34:43'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:34:43'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:44'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:44'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:34:44'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:34:44'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:46'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:46'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:34:46'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:34:46'
}
{
  signal: 'SIGTERM',
  level: 'info',
  message: 'SIGTERM received. Starting graceful shutdown...',
  timestamp: '2025-08-05 00:34:47'
}
{
  message: 'Sentry DSN not configured, error tracking disabled',
  level: 'warn',
  timestamp: '2025-08-05 00:34:47'
}
{
  cors: {
    origin: 'http://localhost:3000',
    methods: [ 'GET', 'POST' ],
    credentials: true
  },
  pingTimeout: 60000,
  pingInterval: 25000,
  level: 'info',
  message: 'Socket.IO server created',
  timestamp: '2025-08-05 00:34:47'
}
{
  port: '3000',
  environment: 'development',
  healthCheck: 'http://localhost:3000/health',
  authAPI: 'http://localhost:3000/api/auth',
  socketIO: 'Socket.IO server initialized',
  level: 'info',
  message: '🚀 Server running on port 3000',
  timestamp: '2025-08-05 00:34:47'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:47'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:47'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:34:47'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:34:47'
}
{
  error: 'Connection is closed.',
  stack: 'Error: Connection is closed.\n' +
    '    at EventEmitter.connectionCloseHandler (/Users/<USER>/Desktop/njback/node_modules/ioredis/built/Redis.js:208:28)\n' +
    '    at Object.onceWrapper (node:events:622:26)\n' +
    '    at EventEmitter.emit (node:events:519:35)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:85:11)',
  level: 'error',
  message: 'Failed to connect to Redis',
  timestamp: '2025-08-05 00:34:47'
}
{
  error: 'Connection is closed.',
  stack: 'Error: Connection is closed.\n' +
    '    at EventEmitter.connectionCloseHandler (/Users/<USER>/Desktop/njback/node_modules/ioredis/built/Redis.js:208:28)\n' +
    '    at Object.onceWrapper (node:events:622:26)\n' +
    '    at EventEmitter.emit (node:events:519:35)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:85:11)',
  context: undefined,
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:47'
}
{
  error: 'Connection is closed.',
  stack: 'Error: Connection is closed.\n' +
    '    at EventEmitter.connectionCloseHandler (/Users/<USER>/Desktop/njback/node_modules/ioredis/built/Redis.js:208:28)\n' +
    '    at Object.onceWrapper (node:events:622:26)\n' +
    '    at EventEmitter.emit (node:events:519:35)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:85:11)',
  level: 'error',
  message: 'Failed to connect Redis for Socket.IO service',
  timestamp: '2025-08-05 00:34:47'
}
{
  error: 'Connection is closed.',
  stack: 'Error: Connection is closed.\n' +
    '    at EventEmitter.connectionCloseHandler (/Users/<USER>/Desktop/njback/node_modules/ioredis/built/Redis.js:208:28)\n' +
    '    at Object.onceWrapper (node:events:622:26)\n' +
    '    at EventEmitter.emit (node:events:519:35)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:85:11)',
  context: undefined,
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:47'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:47'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:47'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:34:47'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:34:47'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:47'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:47'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:34:47'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:34:47'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:48'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:48'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:34:48'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:34:48'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:48'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:48'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:34:48'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:34:48'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:48'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:48'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:34:48'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:34:48'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:48'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:48'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:34:48'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:34:48'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:49'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:49'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:34:49'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:34:49'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:49'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:49'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:34:49'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:34:49'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:50'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:50'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:34:50'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:34:50'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:50'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:50'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:34:50'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:34:50'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:51'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:51'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:34:51'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:34:51'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:51'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:51'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:34:51'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:34:51'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:52'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:52'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:34:52'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:34:52'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:53'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:53'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:34:53'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:34:53'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:53'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:53'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:34:53'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:34:53'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:54'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:54'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:34:54'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:34:54'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:55'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:55'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:34:55'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:34:55'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:56'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:56'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:34:56'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:34:56'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:57'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:57'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:34:57'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:34:57'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:58'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:58'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:34:58'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:34:58'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:59'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:59'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:34:59'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:34:59'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:35:00'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:35:00'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:35:00'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:35:00'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:35:01'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:35:01'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:35:01'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:35:01'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:35:02'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:35:02'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:35:02'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:35:02'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:35:04'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:35:04'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:35:04'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:35:04'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:35:05'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:35:05'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:35:05'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:35:05'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:35:06'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:35:06'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:35:06'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:35:06'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:35:08'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:35:08'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:35:08'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:35:08'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:35:09'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:35:09'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:35:09'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:35:09'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:35:11'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:35:11'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:35:11'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:35:11'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:35:12'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:35:12'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:35:12'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:35:12'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:35:14'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:35:14'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:35:14'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:35:14'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:35:15'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:35:15'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:35:15'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:35:15'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:35:17'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:35:17'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:35:17'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:35:17'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:35:19'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:35:19'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:35:19'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:35:19'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:35:21'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:35:21'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:35:21'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:35:21'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:35:23'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:35:23'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:35:23'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:35:23'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:35:24'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:35:24'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:35:24'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:35:24'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:35:26'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:35:26'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:35:26'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:35:26'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:35:28'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:35:28'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:35:28'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:35:28'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:35:30'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:35:30'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:35:30'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:35:30'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:35:32'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:35:32'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:35:32'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:35:32'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:35:34'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:35:34'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:35:34'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:35:34'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:35:36'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:35:36'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:35:36'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:35:36'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:35:38'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:35:38'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:35:38'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:35:38'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:35:40'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:35:40'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:35:40'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:35:40'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:35:42'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:35:42'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:35:42'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:35:42'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:35:44'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:35:44'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:35:44'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:35:44'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:35:46'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:35:46'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:35:46'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:35:46'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:35:48'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:35:48'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:35:48'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:35:48'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:35:50'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:35:50'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:35:50'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:35:50'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:35:52'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:35:52'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:35:52'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:35:52'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:35:54'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:35:54'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:35:54'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:35:54'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:35:56'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:35:56'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:35:56'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:35:56'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:35:59'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:35:59'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:35:59'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:35:59'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:36:01'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:36:01'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:36:01'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:36:01'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:36:03'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:36:03'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:36:03'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:36:03'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:36:05'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:36:05'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:36:05'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:36:05'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:36:07'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:36:07'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:36:07'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:36:07'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:36:09'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:36:09'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:36:09'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:36:09'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:36:11'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:36:11'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:36:11'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:36:11'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:36:13'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:36:13'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:36:13'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:36:13'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:36:15'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:36:15'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:36:15'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:36:15'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:36:17'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:36:17'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:36:17'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:36:17'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:36:19'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:36:19'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:36:19'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:36:19'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:36:21'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:36:21'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:36:21'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:36:21'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:36:23'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:36:23'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:36:23'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:36:23'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:36:25'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:36:25'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:36:25'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:36:25'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:36:27'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:36:27'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:36:27'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:36:27'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:36:29'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:36:29'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:36:29'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:36:29'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:36:31'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:36:31'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:36:31'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:36:31'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:36:33'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:36:33'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:36:33'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:36:33'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:36:35'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:36:35'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:36:35'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:36:35'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:36:37'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:36:37'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:36:37'
}
{
  level: 'info',
  message: 'Redis reconnecting...',
  timestamp: '2025-08-05 00:36:37'
}
{
  host: 'localhost',
  port: 6379,
  db: 0,
  level: 'info',
  message: 'Redis connected successfully',
  timestamp: '2025-08-05 00:36:39'
}
{
  level: 'info',
  message: 'Redis client ready',
  timestamp: '2025-08-05 00:36:39'
}
{
  host: 'localhost',
  port: 6379,
  db: 0,
  level: 'info',
  message: 'Redis connected successfully',
  timestamp: '2025-08-05 00:38:16'
}
{
  level: 'info',
  message: 'Redis client ready',
  timestamp: '2025-08-05 00:38:16'
}
{
  level: 'info',
  message: 'Redis connection established',
  timestamp: '2025-08-05 00:38:16'
}
{
  count: 0,
  level: 'info',
  message: 'Cleared all online users',
  timestamp: '2025-08-05 00:38:16'
}
{
  count: 0,
  level: 'info',
  message: 'Cleared all online users',
  timestamp: '2025-08-05 00:38:17'
}
{
  level: 'info',
  message: 'Redis disconnected successfully',
  timestamp: '2025-08-05 00:38:17'
}
{
  level: 'warn',
  message: 'Redis connection closed',
  timestamp: '2025-08-05 00:38:17'
}
{
  message: 'Sentry DSN not configured, error tracking disabled',
  level: 'warn',
  timestamp: '2025-08-05 00:38:24'
}
{
  message: 'Sentry DSN not configured, error tracking disabled',
  level: 'warn',
  timestamp: '2025-08-05 00:38:46'
}
{
  cors: {
    origin: 'http://localhost:3000',
    methods: [ 'GET', 'POST' ],
    credentials: true
  },
  pingTimeout: 60000,
  pingInterval: 25000,
  level: 'info',
  message: 'Socket.IO server created',
  timestamp: '2025-08-05 00:38:46'
}
{
  port: '3002',
  environment: 'development',
  healthCheck: 'http://localhost:3002/health',
  authAPI: 'http://localhost:3002/api/auth',
  socketIO: 'Socket.IO server initialized',
  level: 'info',
  message: '🚀 Server running on port 3002',
  timestamp: '2025-08-05 00:38:46'
}
{
  host: 'localhost',
  port: 6379,
  db: 0,
  level: 'info',
  message: 'Redis connected successfully',
  timestamp: '2025-08-05 00:38:46'
}
{
  level: 'info',
  message: 'Redis client ready',
  timestamp: '2025-08-05 00:38:46'
}
{
  level: 'info',
  message: 'Redis connection established',
  timestamp: '2025-08-05 00:38:46'
}
{
  level: 'info',
  message: 'Redis connected for Socket.IO service',
  timestamp: '2025-08-05 00:38:46'
}
{
  requestId: '1b62b887-c5d2-458f-a7ed-d6fa5c59b527',
  method: 'POST',
  url: '/api/auth/register',
  ip: '::1',
  userAgent: 'axios/1.11.0',
  userId: undefined,
  level: 'http',
  message: 'Request started: POST /api/auth/register',
  timestamp: '2025-08-05 00:39:34'
}
{
  requestId: '1b62b887-c5d2-458f-a7ed-d6fa5c59b527',
  method: 'POST',
  url: '/api/auth/register',
  ip: '::1',
  userAgent: 'axios/1.11.0',
  userId: undefined,
  responseTime: 8,
  body: {
    username: 'sockettest',
    email: '<EMAIL>',
    password: '[REDACTED]'
  },
  query: {},
  params: {},
  error: 'Validation failed',
  stack: 'Error: Validation failed\n' +
    '    at /Users/<USER>/Desktop/njback/dist/api/middlewares/validation.middleware.js:18:23\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)',
  level: 'error',
  message: 'Request failed: POST /api/auth/register',
  timestamp: '2025-08-05 00:39:34'
}
{
  error: 'Validation failed',
  stack: 'Error: Validation failed\n' +
    '    at /Users/<USER>/Desktop/njback/dist/api/middlewares/validation.middleware.js:18:23\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)',
  context: {
    requestId: '1b62b887-c5d2-458f-a7ed-d6fa5c59b527',
    method: 'POST',
    url: '/api/auth/register',
    ip: '::1',
    userAgent: 'axios/1.11.0',
    userId: undefined,
    responseTime: 8,
    body: {
      username: 'sockettest',
      email: '<EMAIL>',
      password: '[REDACTED]'
    },
    query: {},
    params: {}
  },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:39:34'
}
{
  method: 'POST',
  url: '/api/auth/register',
  ip: '::1',
  userAgent: 'axios/1.11.0',
  statusCode: 400,
  responseTime: 9,
  userId: undefined,
  category: 'api',
  level: 'error',
  message: '[API] POST /api/auth/register - 400 - 9ms',
  timestamp: '2025-08-05 00:39:34'
}
{
  requestId: 'ff4cb9f0-1cb7-4d46-9613-4edb842812ee',
  method: 'POST',
  url: '/api/auth/register',
  ip: '::1',
  userAgent: 'axios/1.11.0',
  userId: undefined,
  level: 'http',
  message: 'Request started: POST /api/auth/register',
  timestamp: '2025-08-05 00:40:41'
}
{
  email: '<EMAIL>',
  username: 'sockettest',
  category: 'auth',
  level: 'info',
  message: '[AUTH] User registration attempt',
  timestamp: '2025-08-05 00:40:41'
}
{
  userId: '689128d91785dc348a46de6c',
  email: '<EMAIL>',
  username: 'sockettest',
  category: 'auth',
  level: 'info',
  message: '[AUTH] User registration successful',
  timestamp: '2025-08-05 00:40:42'
}
{
  method: 'POST',
  url: '/api/auth/register',
  ip: '::1',
  userAgent: 'axios/1.11.0',
  statusCode: 201,
  responseTime: 222,
  userId: undefined,
  category: 'api',
  level: 'http',
  message: '[API] POST /api/auth/register - 201 - 222ms',
  timestamp: '2025-08-05 00:40:42'
}
{
  requestId: '12fe500b-f05c-4a68-af2d-db30f53603b0',
  method: 'POST',
  url: '/api/auth/login',
  ip: '::1',
  userAgent: 'axios/1.11.0',
  userId: undefined,
  level: 'http',
  message: 'Request started: POST /api/auth/login',
  timestamp: '2025-08-05 00:40:42'
}
{
  email: '<EMAIL>',
  category: 'auth',
  level: 'info',
  message: '[AUTH] User login attempt',
  timestamp: '2025-08-05 00:40:42'
}
{
  userId: '689128d91785dc348a46de6c',
  email: '<EMAIL>',
  username: 'sockettest',
  category: 'auth',
  level: 'info',
  message: '[AUTH] User login successful',
  timestamp: '2025-08-05 00:40:42'
}
{
  method: 'POST',
  url: '/api/auth/login',
  ip: '::1',
  userAgent: 'axios/1.11.0',
  statusCode: 200,
  responseTime: 209,
  userId: undefined,
  category: 'api',
  level: 'http',
  message: '[API] POST /api/auth/login - 200 - 209ms',
  timestamp: '2025-08-05 00:40:42'
}
{
  socketId: 'RUfR6Ze5fht7j99eAAAB',
  ip: '::1',
  userAgent: 'node-XMLHttpRequest',
  category: 'security',
  level: 'warn',
  message: '[SECURITY] Socket connection rejected - no token provided',
  timestamp: '2025-08-05 00:40:42'
}
{
  signal: 'SIGTERM',
  level: 'info',
  message: 'SIGTERM received. Starting graceful shutdown...',
  timestamp: '2025-08-05 00:41:06'
}
{
  message: 'Sentry DSN not configured, error tracking disabled',
  level: 'warn',
  timestamp: '2025-08-05 00:41:06'
}
{
  message: 'Sentry DSN not configured, error tracking disabled',
  level: 'warn',
  timestamp: '2025-08-05 00:41:28'
}
{
  cors: {
    origin: 'http://localhost:3000',
    methods: [ 'GET', 'POST' ],
    credentials: true
  },
  pingTimeout: 60000,
  pingInterval: 25000,
  level: 'info',
  message: 'Socket.IO server created',
  timestamp: '2025-08-05 00:41:28'
}
{
  port: '3002',
  environment: 'development',
  healthCheck: 'http://localhost:3002/health',
  authAPI: 'http://localhost:3002/api/auth',
  socketIO: 'Socket.IO server initialized',
  level: 'info',
  message: '🚀 Server running on port 3002',
  timestamp: '2025-08-05 00:41:28'
}
{
  host: 'localhost',
  port: 6379,
  db: 0,
  level: 'info',
  message: 'Redis connected successfully',
  timestamp: '2025-08-05 00:41:28'
}
{
  level: 'info',
  message: 'Redis client ready',
  timestamp: '2025-08-05 00:41:28'
}
{
  level: 'info',
  message: 'Redis connection established',
  timestamp: '2025-08-05 00:41:28'
}
{
  level: 'info',
  message: 'Redis connected for Socket.IO service',
  timestamp: '2025-08-05 00:41:28'
}
{
  requestId: 'c4709a8f-3caa-4c36-a373-16a37778c7c1',
  method: 'POST',
  url: '/api/auth/register',
  ip: '::1',
  userAgent: 'axios/1.11.0',
  userId: undefined,
  level: 'http',
  message: 'Request started: POST /api/auth/register',
  timestamp: '2025-08-05 00:41:50'
}
{
  email: '<EMAIL>',
  username: 'sockettest',
  category: 'auth',
  level: 'info',
  message: '[AUTH] User registration attempt',
  timestamp: '2025-08-05 00:41:50'
}
{
  email: '<EMAIL>',
  category: 'auth',
  level: 'info',
  message: '[AUTH] Registration failed - email already exists',
  timestamp: '2025-08-05 00:41:50'
}
{
  requestId: 'c4709a8f-3caa-4c36-a373-16a37778c7c1',
  method: 'POST',
  url: '/api/auth/register',
  ip: '::1',
  userAgent: 'axios/1.11.0',
  userId: undefined,
  responseTime: 15,
  body: {
    username: 'sockettest',
    email: '<EMAIL>',
    password: '[REDACTED]'
  },
  query: {},
  params: {},
  error: 'User with this email already exists',
  stack: 'Error: User with this email already exists\n' +
    '    at AuthService.register (/Users/<USER>/Desktop/njback/dist/services/auth.service.js:25:23)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async AuthController.register (/Users/<USER>/Desktop/njback/dist/api/controllers/auth.controller.js:14:32)',
  level: 'error',
  message: 'Request failed: POST /api/auth/register',
  timestamp: '2025-08-05 00:41:50'
}
{
  error: 'User with this email already exists',
  stack: 'Error: User with this email already exists\n' +
    '    at AuthService.register (/Users/<USER>/Desktop/njback/dist/services/auth.service.js:25:23)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async AuthController.register (/Users/<USER>/Desktop/njback/dist/api/controllers/auth.controller.js:14:32)',
  context: {
    requestId: 'c4709a8f-3caa-4c36-a373-16a37778c7c1',
    method: 'POST',
    url: '/api/auth/register',
    ip: '::1',
    userAgent: 'axios/1.11.0',
    userId: undefined,
    responseTime: 15,
    body: {
      username: 'sockettest',
      email: '<EMAIL>',
      password: '[REDACTED]'
    },
    query: {},
    params: {}
  },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:41:50'
}
{
  method: 'POST',
  url: '/api/auth/register',
  ip: '::1',
  userAgent: 'axios/1.11.0',
  statusCode: 400,
  responseTime: 16,
  userId: undefined,
  category: 'api',
  level: 'error',
  message: '[API] POST /api/auth/register - 400 - 16ms',
  timestamp: '2025-08-05 00:41:50'
}
{
  requestId: '7122c7dc-e4b0-4bee-bd5b-20abe5b7c025',
  method: 'POST',
  url: '/api/auth/register',
  ip: '::1',
  userAgent: 'axios/1.11.0',
  userId: undefined,
  level: 'http',
  message: 'Request started: POST /api/auth/register',
  timestamp: '2025-08-05 00:42:21'
}
{
  email: '<EMAIL>',
  username: 'sockettest1754343741248',
  category: 'auth',
  level: 'info',
  message: '[AUTH] User registration attempt',
  timestamp: '2025-08-05 00:42:21'
}
{
  userId: '6891293dc3c3aa3a907084f1',
  email: '<EMAIL>',
  username: 'sockettest1754343741248',
  category: 'auth',
  level: 'info',
  message: '[AUTH] User registration successful',
  timestamp: '2025-08-05 00:42:21'
}
{
  method: 'POST',
  url: '/api/auth/register',
  ip: '::1',
  userAgent: 'axios/1.11.0',
  statusCode: 201,
  responseTime: 216,
  userId: undefined,
  category: 'api',
  level: 'http',
  message: '[API] POST /api/auth/register - 201 - 216ms',
  timestamp: '2025-08-05 00:42:21'
}
{
  requestId: 'af9294a2-22ea-421c-a4bd-1fb4cc36aee1',
  method: 'POST',
  url: '/api/auth/login',
  ip: '::1',
  userAgent: 'axios/1.11.0',
  userId: undefined,
  level: 'http',
  message: 'Request started: POST /api/auth/login',
  timestamp: '2025-08-05 00:42:21'
}
{
  email: '<EMAIL>',
  category: 'auth',
  level: 'info',
  message: '[AUTH] User login attempt',
  timestamp: '2025-08-05 00:42:21'
}
{
  userId: '6891293dc3c3aa3a907084f1',
  email: '<EMAIL>',
  username: 'sockettest1754343741248',
  category: 'auth',
  level: 'info',
  message: '[AUTH] User login successful',
  timestamp: '2025-08-05 00:42:21'
}
{
  method: 'POST',
  url: '/api/auth/login',
  ip: '::1',
  userAgent: 'axios/1.11.0',
  statusCode: 200,
  responseTime: 202,
  userId: undefined,
  category: 'api',
  level: 'http',
  message: '[API] POST /api/auth/login - 200 - 202ms',
  timestamp: '2025-08-05 00:42:21'
}
{
  socketId: 'C7uB1pCb9_6fHR50AAAB',
  ip: '::1',
  userAgent: 'node-XMLHttpRequest',
  category: 'security',
  level: 'warn',
  message: '[SECURITY] Socket connection rejected - no token provided',
  timestamp: '2025-08-05 00:42:21'
}
{
  message: 'Sentry DSN not configured, error tracking disabled',
  level: 'warn',
  timestamp: '2025-08-05 00:42:39'
}
{
  message: 'Sentry DSN not configured, error tracking disabled',
  level: 'warn',
  timestamp: '2025-08-05 00:43:05'
}
{
  cors: {
    origin: 'http://localhost:3000',
    methods: [ 'GET', 'POST' ],
    credentials: true
  },
  pingTimeout: 60000,
  pingInterval: 25000,
  level: 'info',
  message: 'Socket.IO server created',
  timestamp: '2025-08-05 00:43:05'
}
{
  port: '3002',
  environment: 'development',
  healthCheck: 'http://localhost:3002/health',
  authAPI: 'http://localhost:3002/api/auth',
  socketIO: 'Socket.IO server initialized',
  level: 'info',
  message: '🚀 Server running on port 3002',
  timestamp: '2025-08-05 00:43:05'
}
{
  host: 'localhost',
  port: 6379,
  db: 0,
  level: 'info',
  message: 'Redis connected successfully',
  timestamp: '2025-08-05 00:43:05'
}
{
  level: 'info',
  message: 'Redis client ready',
  timestamp: '2025-08-05 00:43:05'
}
{
  level: 'info',
  message: 'Redis connection established',
  timestamp: '2025-08-05 00:43:05'
}
{
  level: 'info',
  message: 'Redis connected for Socket.IO service',
  timestamp: '2025-08-05 00:43:05'
}
{
  requestId: 'a09a423e-63b1-48e3-b0a0-92d334b335d0',
  method: 'POST',
  url: '/api/auth/register',
  ip: '::1',
  userAgent: 'axios/1.11.0',
  userId: undefined,
  level: 'http',
  message: 'Request started: POST /api/auth/register',
  timestamp: '2025-08-05 00:43:27'
}
{
  email: '<EMAIL>',
  username: 'sockettest1754343807003',
  category: 'auth',
  level: 'info',
  message: '[AUTH] User registration attempt',
  timestamp: '2025-08-05 00:43:27'
}
{
  userId: '6891297f8aac8a6ccbb5ed78',
  email: '<EMAIL>',
  username: 'sockettest1754343807003',
  category: 'auth',
  level: 'info',
  message: '[AUTH] User registration successful',
  timestamp: '2025-08-05 00:43:27'
}
{
  method: 'POST',
  url: '/api/auth/register',
  ip: '::1',
  userAgent: 'axios/1.11.0',
  statusCode: 201,
  responseTime: 225,
  userId: undefined,
  category: 'api',
  level: 'http',
  message: '[API] POST /api/auth/register - 201 - 225ms',
  timestamp: '2025-08-05 00:43:27'
}
{
  requestId: '0f2c729f-3fd6-42b2-bae0-e55ff34e960f',
  method: 'POST',
  url: '/api/auth/login',
  ip: '::1',
  userAgent: 'axios/1.11.0',
  userId: undefined,
  level: 'http',
  message: 'Request started: POST /api/auth/login',
  timestamp: '2025-08-05 00:43:27'
}
{
  email: '<EMAIL>',
  category: 'auth',
  level: 'info',
  message: '[AUTH] User login attempt',
  timestamp: '2025-08-05 00:43:27'
}
{
  userId: '6891297f8aac8a6ccbb5ed78',
  email: '<EMAIL>',
  username: 'sockettest1754343807003',
  category: 'auth',
  level: 'info',
  message: '[AUTH] User login successful',
  timestamp: '2025-08-05 00:43:27'
}
{
  method: 'POST',
  url: '/api/auth/login',
  ip: '::1',
  userAgent: 'axios/1.11.0',
  statusCode: 200,
  responseTime: 202,
  userId: undefined,
  category: 'api',
  level: 'http',
  message: '[API] POST /api/auth/login - 200 - 202ms',
  timestamp: '2025-08-05 00:43:27'
}
{
  auth: {},
  headers: {
    'user-agent': 'node-XMLHttpRequest',
    accept: '*/*',
    host: 'localhost:3002',
    connection: 'close'
  },
  query: [Object: null prototype] {
    EIO: '4',
    transport: 'polling',
    t: 'n0992ecw'
  },
  level: 'debug',
  message: 'Socket handshake debug',
  timestamp: '2025-08-05 00:43:27'
}
{
  socketId: 'kPw_1PtOCY2yikscAAAB',
  ip: '::1',
  userAgent: 'node-XMLHttpRequest',
  category: 'security',
  level: 'warn',
  message: '[SECURITY] Socket connection rejected - no token provided',
  timestamp: '2025-08-05 00:43:27'
}
{
  requestId: '4924f309-2383-4e32-9db0-b40b1e3d5bce',
  method: 'POST',
  url: '/api/auth/register',
  ip: '::1',
  userAgent: 'axios/1.11.0',
  userId: undefined,
  level: 'http',
  message: 'Request started: POST /api/auth/register',
  timestamp: '2025-08-05 00:43:53'
}
{
  email: '<EMAIL>',
  username: 'sockettest1754343833622',
  category: 'auth',
  level: 'info',
  message: '[AUTH] User registration attempt',
  timestamp: '2025-08-05 00:43:53'
}
{
  userId: '689129998aac8a6ccbb5ed7d',
  email: '<EMAIL>',
  username: 'sockettest1754343833622',
  category: 'auth',
  level: 'info',
  message: '[AUTH] User registration successful',
  timestamp: '2025-08-05 00:43:53'
}
{
  method: 'POST',
  url: '/api/auth/register',
  ip: '::1',
  userAgent: 'axios/1.11.0',
  statusCode: 201,
  responseTime: 207,
  userId: undefined,
  category: 'api',
  level: 'http',
  message: '[API] POST /api/auth/register - 201 - 207ms',
  timestamp: '2025-08-05 00:43:53'
}
{
  requestId: 'e0ccf61a-821c-44cd-91cc-bf0b1b6ef9b9',
  method: 'POST',
  url: '/api/auth/login',
  ip: '::1',
  userAgent: 'axios/1.11.0',
  userId: undefined,
  level: 'http',
  message: 'Request started: POST /api/auth/login',
  timestamp: '2025-08-05 00:43:53'
}
{
  email: '<EMAIL>',
  category: 'auth',
  level: 'info',
  message: '[AUTH] User login attempt',
  timestamp: '2025-08-05 00:43:53'
}
{
  userId: '689129998aac8a6ccbb5ed7d',
  email: '<EMAIL>',
  username: 'sockettest1754343833622',
  category: 'auth',
  level: 'info',
  message: '[AUTH] User login successful',
  timestamp: '2025-08-05 00:43:54'
}
{
  method: 'POST',
  url: '/api/auth/login',
  ip: '::1',
  userAgent: 'axios/1.11.0',
  statusCode: 200,
  responseTime: 201,
  userId: undefined,
  category: 'api',
  level: 'http',
  message: '[API] POST /api/auth/login - 200 - 201ms',
  timestamp: '2025-08-05 00:43:54'
}
{
  auth: {},
  headers: {
    'user-agent': 'node-XMLHttpRequest',
    accept: '*/*',
    authorization: 'Bearer undefined',
    host: 'localhost:3002',
    connection: 'close'
  },
  query: [Object: null prototype] {
    token: 'undefined',
    EIO: '4',
    transport: 'polling',
    t: 'n0trvswx'
  },
  level: 'debug',
  message: 'Socket handshake debug',
  timestamp: '2025-08-05 00:43:54'
}
{
  socketId: 'NLzpXFwQUNmvsZPlAAAD',
  ip: '::1',
  userAgent: 'node-XMLHttpRequest',
  error: 'jwt malformed',
  stack: 'JsonWebTokenError: jwt malformed\n' +
    '    at module.exports [as verify] (/Users/<USER>/Desktop/njback/node_modules/jsonwebtoken/verify.js:70:17)\n' +
    '    at Array.socketAuthMiddleware (/Users/<USER>/Desktop/njback/dist/api/middlewares/socket.middleware.js:39:48)\n' +
    '    at run (/Users/<USER>/Desktop/njback/node_modules/socket.io/dist/namespace.js:130:19)\n' +
    '    at Namespace.run (/Users/<USER>/Desktop/njback/node_modules/socket.io/dist/namespace.js:141:9)\n' +
    '    at Namespace._add (/Users/<USER>/Desktop/njback/node_modules/socket.io/dist/namespace.js:218:14)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)',
  level: 'error',
  message: 'Socket authentication failed',
  timestamp: '2025-08-05 00:43:54'
}
{
  error: 'jwt malformed',
  stack: 'JsonWebTokenError: jwt malformed\n' +
    '    at module.exports [as verify] (/Users/<USER>/Desktop/njback/node_modules/jsonwebtoken/verify.js:70:17)\n' +
    '    at Array.socketAuthMiddleware (/Users/<USER>/Desktop/njback/dist/api/middlewares/socket.middleware.js:39:48)\n' +
    '    at run (/Users/<USER>/Desktop/njback/node_modules/socket.io/dist/namespace.js:130:19)\n' +
    '    at Namespace.run (/Users/<USER>/Desktop/njback/node_modules/socket.io/dist/namespace.js:141:9)\n' +
    '    at Namespace._add (/Users/<USER>/Desktop/njback/node_modules/socket.io/dist/namespace.js:218:14)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)',
  context: {
    socketId: 'NLzpXFwQUNmvsZPlAAAD',
    ip: '::1',
    userAgent: 'node-XMLHttpRequest'
  },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:43:54'
}
{
  requestId: '45f30f7b-5027-4be1-9bbf-2dfe13b1f5a0',
  method: 'POST',
  url: '/api/auth/register',
  ip: '::1',
  userAgent: 'axios/1.11.0',
  userId: undefined,
  level: 'http',
  message: 'Request started: POST /api/auth/register',
  timestamp: '2025-08-05 00:44:32'
}
{
  email: '<EMAIL>',
  username: 'sockettest1754343872142',
  category: 'auth',
  level: 'info',
  message: '[AUTH] User registration attempt',
  timestamp: '2025-08-05 00:44:32'
}
{
  userId: '689129c08aac8a6ccbb5ed82',
  email: '<EMAIL>',
  username: 'sockettest1754343872142',
  category: 'auth',
  level: 'info',
  message: '[AUTH] User registration successful',
  timestamp: '2025-08-05 00:44:32'
}
{
  method: 'POST',
  url: '/api/auth/register',
  ip: '::1',
  userAgent: 'axios/1.11.0',
  statusCode: 201,
  responseTime: 206,
  userId: undefined,
  category: 'api',
  level: 'http',
  message: '[API] POST /api/auth/register - 201 - 206ms',
  timestamp: '2025-08-05 00:44:32'
}
{
  requestId: '3a69bdc6-91b1-421f-8c54-c158c0f1a160',
  method: 'POST',
  url: '/api/auth/login',
  ip: '::1',
  userAgent: 'axios/1.11.0',
  userId: undefined,
  level: 'http',
  message: 'Request started: POST /api/auth/login',
  timestamp: '2025-08-05 00:44:32'
}
{
  email: '<EMAIL>',
  category: 'auth',
  level: 'info',
  message: '[AUTH] User login attempt',
  timestamp: '2025-08-05 00:44:32'
}
{
  userId: '689129c08aac8a6ccbb5ed82',
  email: '<EMAIL>',
  username: 'sockettest1754343872142',
  category: 'auth',
  level: 'info',
  message: '[AUTH] User login successful',
  timestamp: '2025-08-05 00:44:32'
}
{
  method: 'POST',
  url: '/api/auth/login',
  ip: '::1',
  userAgent: 'axios/1.11.0',
  statusCode: 200,
  responseTime: 201,
  userId: undefined,
  category: 'api',
  level: 'http',
  message: '[API] POST /api/auth/login - 200 - 201ms',
  timestamp: '2025-08-05 00:44:32'
}
{
  auth: {},
  headers: {
    'user-agent': 'node-XMLHttpRequest',
    accept: '*/*',
    authorization: 'Bearer undefined',
    host: 'localhost:3002',
    connection: 'close'
  },
  query: [Object: null prototype] {
    token: 'undefined',
    EIO: '4',
    transport: 'polling',
    t: 'n1nhvl9i'
  },
  level: 'debug',
  message: 'Socket handshake debug',
  timestamp: '2025-08-05 00:44:32'
}
{
  socketId: 'fTF4-5z-giDXFWu1AAAF',
  ip: '::1',
  userAgent: 'node-XMLHttpRequest',
  error: 'jwt malformed',
  stack: 'JsonWebTokenError: jwt malformed\n' +
    '    at module.exports [as verify] (/Users/<USER>/Desktop/njback/node_modules/jsonwebtoken/verify.js:70:17)\n' +
    '    at Array.socketAuthMiddleware (/Users/<USER>/Desktop/njback/dist/api/middlewares/socket.middleware.js:39:48)\n' +
    '    at run (/Users/<USER>/Desktop/njback/node_modules/socket.io/dist/namespace.js:130:19)\n' +
    '    at Namespace.run (/Users/<USER>/Desktop/njback/node_modules/socket.io/dist/namespace.js:141:9)\n' +
    '    at Namespace._add (/Users/<USER>/Desktop/njback/node_modules/socket.io/dist/namespace.js:218:14)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)',
  level: 'error',
  message: 'Socket authentication failed',
  timestamp: '2025-08-05 00:44:32'
}
{
  error: 'jwt malformed',
  stack: 'JsonWebTokenError: jwt malformed\n' +
    '    at module.exports [as verify] (/Users/<USER>/Desktop/njback/node_modules/jsonwebtoken/verify.js:70:17)\n' +
    '    at Array.socketAuthMiddleware (/Users/<USER>/Desktop/njback/dist/api/middlewares/socket.middleware.js:39:48)\n' +
    '    at run (/Users/<USER>/Desktop/njback/node_modules/socket.io/dist/namespace.js:130:19)\n' +
    '    at Namespace.run (/Users/<USER>/Desktop/njback/node_modules/socket.io/dist/namespace.js:141:9)\n' +
    '    at Namespace._add (/Users/<USER>/Desktop/njback/node_modules/socket.io/dist/namespace.js:218:14)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)',
  context: {
    socketId: 'fTF4-5z-giDXFWu1AAAF',
    ip: '::1',
    userAgent: 'node-XMLHttpRequest'
  },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:44:32'
}
{
  requestId: '0ba2f1a6-aa72-4fd2-b90f-e1595d080e62',
  method: 'POST',
  url: '/api/auth/register',
  ip: '::1',
  userAgent: 'axios/1.11.0',
  userId: undefined,
  level: 'http',
  message: 'Request started: POST /api/auth/register',
  timestamp: '2025-08-05 00:44:53'
}
{
  email: '<EMAIL>',
  username: 'sockettest1754343893753',
  category: 'auth',
  level: 'info',
  message: '[AUTH] User registration attempt',
  timestamp: '2025-08-05 00:44:53'
}
{
  userId: '689129d58aac8a6ccbb5ed87',
  email: '<EMAIL>',
  username: 'sockettest1754343893753',
  category: 'auth',
  level: 'info',
  message: '[AUTH] User registration successful',
  timestamp: '2025-08-05 00:44:53'
}
{
  method: 'POST',
  url: '/api/auth/register',
  ip: '::1',
  userAgent: 'axios/1.11.0',
  statusCode: 201,
  responseTime: 206,
  userId: undefined,
  category: 'api',
  level: 'http',
  message: '[API] POST /api/auth/register - 201 - 206ms',
  timestamp: '2025-08-05 00:44:53'
}
{
  requestId: 'be63e9ef-60a5-4192-9e22-7413bc2c5237',
  method: 'POST',
  url: '/api/auth/login',
  ip: '::1',
  userAgent: 'axios/1.11.0',
  userId: undefined,
  level: 'http',
  message: 'Request started: POST /api/auth/login',
  timestamp: '2025-08-05 00:44:53'
}
{
  email: '<EMAIL>',
  category: 'auth',
  level: 'info',
  message: '[AUTH] User login attempt',
  timestamp: '2025-08-05 00:44:53'
}
{
  userId: '689129d58aac8a6ccbb5ed87',
  email: '<EMAIL>',
  username: 'sockettest1754343893753',
  category: 'auth',
  level: 'info',
  message: '[AUTH] User login successful',
  timestamp: '2025-08-05 00:44:54'
}
{
  method: 'POST',
  url: '/api/auth/login',
  ip: '::1',
  userAgent: 'axios/1.11.0',
  statusCode: 200,
  responseTime: 199,
  userId: undefined,
  category: 'api',
  level: 'http',
  message: '[API] POST /api/auth/login - 200 - 199ms',
  timestamp: '2025-08-05 00:44:54'
}
{
  auth: {
    token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************.6C_HsWeM1R44SmOsmYLndatjvULKywxDt3VRkfhIOiY'
  },
  headers: {
    'user-agent': 'node-XMLHttpRequest',
    accept: '*/*',
    authorization: 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************.6C_HsWeM1R44SmOsmYLndatjvULKywxDt3VRkfhIOiY',
    host: 'localhost:3002',
    connection: 'close'
  },
  query: [Object: null prototype] {
    token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************.6C_HsWeM1R44SmOsmYLndatjvULKywxDt3VRkfhIOiY',
    EIO: '4',
    transport: 'polling',
    t: 'n2463oqn'
  },
  level: 'debug',
  message: 'Socket handshake debug',
  timestamp: '2025-08-05 00:44:54'
}
{
  socketId: 'Hs1S5LGqsfClD2NWAAAH',
  userId: '689129d58aac8a6ccbb5ed87',
  username: 'sockettest1754343893753',
  ip: '::1',
  category: 'auth',
  level: 'info',
  message: '[AUTH] Socket connection authenticated',
  timestamp: '2025-08-05 00:44:54'
}
{
  socketId: 'Hs1S5LGqsfClD2NWAAAH',
  userId: '689129d58aac8a6ccbb5ed87',
  username: 'sockettest1754343893753',
  totalConnections: 1,
  level: 'info',
  message: 'User connected to Socket.IO',
  timestamp: '2025-08-05 00:44:54'
}
{
  userId: '689129d58aac8a6ccbb5ed87',
  username: 'sockettest1754343893753',
  timestamp: '2025-08-05 00:44:54',
  level: 'info',
  message: 'User set online'
}
{
  userId: '689129d58aac8a6ccbb5ed87',
  username: 'sockettest1754343893753',
  timestamp: '2025-08-05 00:44:54',
  socketId: 'Hs1S5LGqsfClD2NWAAAH',
  level: 'info',
  message: 'User online event broadcasted'
}
{
  userId: '689129d58aac8a6ccbb5ed87',
  socketId: 'Hs1S5LGqsfClD2NWAAAH',
  onlineUsersCount: 0,
  level: 'debug',
  message: 'Online users sent to socket',
  timestamp: '2025-08-05 00:44:54'
}
{
  userId: '689129d58aac8a6ccbb5ed87',
  socketId: 'Hs1S5LGqsfClD2NWAAAH',
  onlineUsersCount: 0,
  level: 'debug',
  message: 'Online users sent to socket',
  timestamp: '2025-08-05 00:44:57'
}
{
  socketId: 'Hs1S5LGqsfClD2NWAAAH',
  userId: '689129d58aac8a6ccbb5ed87',
  username: 'sockettest1754343893753',
  reason: 'transport close',
  totalConnections: 0,
  level: 'info',
  message: 'User disconnected from Socket.IO',
  timestamp: '2025-08-05 00:44:57'
}
{
  userId: '689129d58aac8a6ccbb5ed87',
  level: 'info',
  message: 'User set offline',
  timestamp: '2025-08-05 00:44:57'
}
{
  userId: '689129d58aac8a6ccbb5ed87',
  username: 'sockettest1754343893753',
  timestamp: '2025-08-05 00:44:57',
  socketId: 'Hs1S5LGqsfClD2NWAAAH',
  level: 'info',
  message: 'User offline event broadcasted'
}
{
  requestId: '4ba6bbe1-5163-4daa-a0d0-aaa9d8c54026',
  method: 'POST',
  url: '/api/auth/register',
  ip: '::1',
  userAgent: 'axios/1.11.0',
  userId: undefined,
  level: 'http',
  message: 'Request started: POST /api/auth/register',
  timestamp: '2025-08-05 00:45:30'
}
{
  email: '<EMAIL>',
  username: 'sockettest1754343930322',
  category: 'auth',
  level: 'info',
  message: '[AUTH] User registration attempt',
  timestamp: '2025-08-05 00:45:30'
}
{
  userId: '689129fa8aac8a6ccbb5ed8d',
  email: '<EMAIL>',
  username: 'sockettest1754343930322',
  category: 'auth',
  level: 'info',
  message: '[AUTH] User registration successful',
  timestamp: '2025-08-05 00:45:30'
}
{
  method: 'POST',
  url: '/api/auth/register',
  ip: '::1',
  userAgent: 'axios/1.11.0',
  statusCode: 201,
  responseTime: 204,
  userId: undefined,
  category: 'api',
  level: 'http',
  message: '[API] POST /api/auth/register - 201 - 204ms',
  timestamp: '2025-08-05 00:45:30'
}
{
  requestId: 'f6628d7a-918d-44a3-9d9c-8ee8512c65ad',
  method: 'POST',
  url: '/api/auth/login',
  ip: '::1',
  userAgent: 'axios/1.11.0',
  userId: undefined,
  level: 'http',
  message: 'Request started: POST /api/auth/login',
  timestamp: '2025-08-05 00:45:30'
}
{
  email: '<EMAIL>',
  category: 'auth',
  level: 'info',
  message: '[AUTH] User login attempt',
  timestamp: '2025-08-05 00:45:30'
}
{
  userId: '689129fa8aac8a6ccbb5ed8d',
  email: '<EMAIL>',
  username: 'sockettest1754343930322',
  category: 'auth',
  level: 'info',
  message: '[AUTH] User login successful',
  timestamp: '2025-08-05 00:45:30'
}
{
  method: 'POST',
  url: '/api/auth/login',
  ip: '::1',
  userAgent: 'axios/1.11.0',
  statusCode: 200,
  responseTime: 199,
  userId: undefined,
  category: 'api',
  level: 'http',
  message: '[API] POST /api/auth/login - 200 - 199ms',
  timestamp: '2025-08-05 00:45:30'
}
{
  auth: {
    token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************.qeQwGCv_M3z6amqE21BZde-dXoPK-jQBKqcBhsapLXk'
  },
  headers: {
    'user-agent': 'node-XMLHttpRequest',
    accept: '*/*',
    authorization: 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************.qeQwGCv_M3z6amqE21BZde-dXoPK-jQBKqcBhsapLXk',
    host: 'localhost:3002',
    connection: 'close'
  },
  query: [Object: null prototype] {
    token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************.qeQwGCv_M3z6amqE21BZde-dXoPK-jQBKqcBhsapLXk',
    EIO: '4',
    transport: 'polling',
    t: 'n2wdvqof'
  },
  level: 'debug',
  message: 'Socket handshake debug',
  timestamp: '2025-08-05 00:45:30'
}
{
  socketId: 'dGbQeQJjXnN2gzT3AAAJ',
  userId: '689129fa8aac8a6ccbb5ed8d',
  username: 'sockettest1754343930322',
  ip: '::1',
  category: 'auth',
  level: 'info',
  message: '[AUTH] Socket connection authenticated',
  timestamp: '2025-08-05 00:45:30'
}
{
  socketId: 'dGbQeQJjXnN2gzT3AAAJ',
  userId: '689129fa8aac8a6ccbb5ed8d',
  username: 'sockettest1754343930322',
  totalConnections: 1,
  level: 'info',
  message: 'User connected to Socket.IO',
  timestamp: '2025-08-05 00:45:30'
}
{
  userId: '689129fa8aac8a6ccbb5ed8d',
  username: 'sockettest1754343930322',
  timestamp: '2025-08-05 00:45:30',
  level: 'info',
  message: 'User set online'
}
{
  userId: '689129fa8aac8a6ccbb5ed8d',
  username: 'sockettest1754343930322',
  timestamp: '2025-08-05 00:45:30',
  socketId: 'dGbQeQJjXnN2gzT3AAAJ',
  level: 'info',
  message: 'User online event broadcasted'
}
{
  userId: '689129fa8aac8a6ccbb5ed8d',
  socketId: 'dGbQeQJjXnN2gzT3AAAJ',
  onlineUsersCount: 0,
  level: 'debug',
  message: 'Online users sent to socket',
  timestamp: '2025-08-05 00:45:30'
}
{
  requesterId: '689129fa8aac8a6ccbb5ed8d',
  targetUserId: '689129fa8aac8a6ccbb5ed8d',
  isOnline: true,
  level: 'debug',
  message: 'User status sent',
  timestamp: '2025-08-05 00:45:33'
}
{
  userId: '689129fa8aac8a6ccbb5ed8d',
  socketId: 'dGbQeQJjXnN2gzT3AAAJ',
  onlineUsersCount: 0,
  level: 'debug',
  message: 'Online users sent to socket',
  timestamp: '2025-08-05 00:45:33'
}
{
  socketId: 'dGbQeQJjXnN2gzT3AAAJ',
  userId: '689129fa8aac8a6ccbb5ed8d',
  username: 'sockettest1754343930322',
  reason: 'client namespace disconnect',
  totalConnections: 0,
  level: 'info',
  message: 'User disconnected from Socket.IO',
  timestamp: '2025-08-05 00:45:35'
}
{
  userId: '689129fa8aac8a6ccbb5ed8d',
  level: 'info',
  message: 'User set offline',
  timestamp: '2025-08-05 00:45:35'
}
{
  userId: '689129fa8aac8a6ccbb5ed8d',
  username: 'sockettest1754343930322',
  timestamp: '2025-08-05 00:45:35',
  socketId: 'dGbQeQJjXnN2gzT3AAAJ',
  level: 'info',
  message: 'User offline event broadcasted'
}
{
  message: 'Sentry DSN not configured, error tracking disabled',
  level: 'warn',
  timestamp: '2025-08-05 00:45:54'
}
{
  message: 'Sentry DSN not configured, error tracking disabled',
  level: 'warn',
  timestamp: '2025-08-05 00:47:43'
}
{
  message: 'Sentry DSN not configured, error tracking disabled',
  level: 'warn',
  timestamp: '2025-08-05 00:47:45'
}
{
  message: 'Sentry DSN not configured, error tracking disabled',
  level: 'warn',
  timestamp: '2025-08-05 00:47:49'
}
{
  message: 'Sentry DSN not configured, error tracking disabled',
  level: 'warn',
  timestamp: '2025-08-05 00:47:53'
}
{
  message: 'Sentry DSN not configured, error tracking disabled',
  level: 'warn',
  timestamp: '2025-08-05 00:47:55'
}
{
  message: 'Sentry DSN not configured, error tracking disabled',
  level: 'warn',
  timestamp: '2025-08-05 00:48:21'
}
{
  message: 'Sentry DSN not configured, error tracking disabled',
  level: 'warn',
  timestamp: '2025-08-05 00:48:24'
}
{
  message: 'Sentry DSN not configured, error tracking disabled',
  level: 'warn',
  timestamp: '2025-08-05 00:48:25'
}
{
  message: 'Sentry DSN not configured, error tracking disabled',
  level: 'warn',
  timestamp: '2025-08-05 00:48:27'
}
{
  message: 'Sentry DSN not configured, error tracking disabled',
  level: 'warn',
  timestamp: '2025-08-05 00:48:31'
}
{
  message: 'Sentry DSN not configured, error tracking disabled',
  level: 'warn',
  timestamp: '2025-08-05 00:48:32'
}
{
  signal: 'SIGINT',
  level: 'info',
  message: 'SIGINT received. Starting graceful shutdown...',
  timestamp: '2025-08-05 00:52:42'
}
{
  level: 'info',
  message: 'HTTP server closed',
  timestamp: '2025-08-05 00:52:42'
}
{
  signal: 'SIGINT',
  level: 'info',
  message: 'SIGINT received. Starting graceful shutdown...',
  timestamp: '2025-08-05 00:52:42'
}
{
  level: 'info',
  message: 'HTTP server closed',
  timestamp: '2025-08-05 00:52:42'
}
{
  message: 'Sentry DSN not configured, error tracking disabled',
  level: 'warn',
  timestamp: '2025-08-05 01:01:02'
}
{
  cors: {
    origin: 'http://localhost:3000',
    methods: [ 'GET', 'POST' ],
    credentials: true
  },
  pingTimeout: 60000,
  pingInterval: 25000,
  level: 'info',
  message: 'Socket.IO server created',
  timestamp: '2025-08-05 01:01:02'
}
{
  port: '3002',
  environment: 'development',
  healthCheck: 'http://localhost:3002/health',
  authAPI: 'http://localhost:3002/api/auth',
  socketIO: 'Socket.IO server initialized',
  level: 'info',
  message: '🚀 Server running on port 3002',
  timestamp: '2025-08-05 01:01:02'
}
{
  host: 'localhost',
  port: 6379,
  db: 0,
  level: 'info',
  message: 'Redis connected successfully',
  timestamp: '2025-08-05 01:01:02'
}
{
  level: 'info',
  message: 'Redis client ready',
  timestamp: '2025-08-05 01:01:02'
}
{
  level: 'info',
  message: 'Redis connection established',
  timestamp: '2025-08-05 01:01:02'
}
{
  level: 'info',
  message: 'Redis connected for Socket.IO service',
  timestamp: '2025-08-05 01:01:02'
}
{
  requestId: '344535c6-45d4-4acb-8c24-0ea910e05c8b',
  method: 'POST',
  url: '/api/auth/register',
  ip: '::1',
  userAgent: 'axios/1.11.0',
  userId: undefined,
  level: 'http',
  message: 'Request started: POST /api/auth/register',
  timestamp: '2025-08-05 01:02:01'
}
{
  email: '<EMAIL>',
  username: 'testuser1_1754344921319',
  category: 'auth',
  level: 'info',
  message: '[AUTH] User registration attempt',
  timestamp: '2025-08-05 01:02:01'
}
{
  userId: '68912dd9ef87f5d380f7aa4d',
  email: '<EMAIL>',
  username: 'testuser1_1754344921319',
  category: 'auth',
  level: 'info',
  message: '[AUTH] User registration successful',
  timestamp: '2025-08-05 01:02:01'
}
{
  method: 'POST',
  url: '/api/auth/register',
  ip: '::1',
  userAgent: 'axios/1.11.0',
  statusCode: 201,
  responseTime: 226,
  userId: undefined,
  category: 'api',
  level: 'http',
  message: '[API] POST /api/auth/register - 201 - 226ms',
  timestamp: '2025-08-05 01:02:01'
}
{
  requestId: '7f758233-07e3-4257-abd8-57a8f0a95fbc',
  method: 'POST',
  url: '/api/auth/register',
  ip: '::1',
  userAgent: 'axios/1.11.0',
  userId: undefined,
  level: 'http',
  message: 'Request started: POST /api/auth/register',
  timestamp: '2025-08-05 01:02:01'
}
{
  email: '<EMAIL>',
  username: 'testuser2_1754344921319',
  category: 'auth',
  level: 'info',
  message: '[AUTH] User registration attempt',
  timestamp: '2025-08-05 01:02:01'
}
{
  userId: '68912dd9ef87f5d380f7aa51',
  email: '<EMAIL>',
  username: 'testuser2_1754344921319',
  category: 'auth',
  level: 'info',
  message: '[AUTH] User registration successful',
  timestamp: '2025-08-05 01:02:01'
}
{
  method: 'POST',
  url: '/api/auth/register',
  ip: '::1',
  userAgent: 'axios/1.11.0',
  statusCode: 201,
  responseTime: 209,
  userId: undefined,
  category: 'api',
  level: 'http',
  message: '[API] POST /api/auth/register - 201 - 209ms',
  timestamp: '2025-08-05 01:02:01'
}
{
  requestId: 'd1dfdbcb-f9fc-439e-b94c-e2cc2472c4e1',
  method: 'POST',
  url: '/api/auth/login',
  ip: '::1',
  userAgent: 'axios/1.11.0',
  userId: undefined,
  level: 'http',
  message: 'Request started: POST /api/auth/login',
  timestamp: '2025-08-05 01:02:01'
}
{
  email: '<EMAIL>',
  category: 'auth',
  level: 'info',
  message: '[AUTH] User login attempt',
  timestamp: '2025-08-05 01:02:01'
}
{
  userId: '68912dd9ef87f5d380f7aa4d',
  email: '<EMAIL>',
  username: 'testuser1_1754344921319',
  category: 'auth',
  level: 'info',
  message: '[AUTH] User login successful',
  timestamp: '2025-08-05 01:02:01'
}
{
  method: 'POST',
  url: '/api/auth/login',
  ip: '::1',
  userAgent: 'axios/1.11.0',
  statusCode: 200,
  responseTime: 205,
  userId: undefined,
  category: 'api',
  level: 'http',
  message: '[API] POST /api/auth/login - 200 - 205ms',
  timestamp: '2025-08-05 01:02:01'
}
{
  requestId: '2d689449-7227-44e0-a32b-fd13856ec1ff',
  method: 'POST',
  url: '/api/auth/login',
  ip: '::1',
  userAgent: 'axios/1.11.0',
  userId: undefined,
  level: 'http',
  message: 'Request started: POST /api/auth/login',
  timestamp: '2025-08-05 01:02:01'
}
{
  email: '<EMAIL>',
  category: 'auth',
  level: 'info',
  message: '[AUTH] User login attempt',
  timestamp: '2025-08-05 01:02:01'
}
{
  userId: '68912dd9ef87f5d380f7aa51',
  email: '<EMAIL>',
  username: 'testuser2_1754344921319',
  category: 'auth',
  level: 'info',
  message: '[AUTH] User login successful',
  timestamp: '2025-08-05 01:02:02'
}
{
  method: 'POST',
  url: '/api/auth/login',
  ip: '::1',
  userAgent: 'axios/1.11.0',
  statusCode: 200,
  responseTime: 200,
  userId: undefined,
  category: 'api',
  level: 'http',
  message: '[API] POST /api/auth/login - 200 - 200ms',
  timestamp: '2025-08-05 01:02:02'
}
{
  requestId: 'ba4cc11b-54a9-4837-a0d6-f06953c820cb',
  method: 'POST',
  url: '/api/conversations',
  ip: '::1',
  userAgent: 'axios/1.11.0',
  userId: undefined,
  level: 'http',
  message: 'Request started: POST /api/conversations',
  timestamp: '2025-08-05 01:02:02'
}
{
  currentUserId: '68912dd9ef87f5d380f7aa4d',
  participantId: '68912dd9ef87f5d380f7aa51',
  category: 'conversation',
  level: 'info',
  message: '[CONVERSATION] Create conversation attempt',
  timestamp: '2025-08-05 01:02:02'
}
{
  conversationId: new ObjectId('68912ddaef87f5d380f7aa57'),
  participants: [ '68912dd9ef87f5d380f7aa4d', '68912dd9ef87f5d380f7aa51' ],
  category: 'conversation',
  level: 'info',
  message: 'New conversation created',
  timestamp: '2025-08-05 01:02:02'
}
{
  conversationId: new ObjectId('68912ddaef87f5d380f7aa57'),
  currentUserId: '68912dd9ef87f5d380f7aa4d',
  participantId: '68912dd9ef87f5d380f7aa51',
  isNew: true,
  category: 'conversation',
  level: 'info',
  message: '[CONVERSATION] Create conversation successful',
  timestamp: '2025-08-05 01:02:02'
}
{
  method: 'POST',
  url: '/api/conversations',
  ip: '::1',
  userAgent: 'axios/1.11.0',
  statusCode: 201,
  responseTime: 18,
  userId: '68912dd9ef87f5d380f7aa4d',
  category: 'api',
  level: 'http',
  message: '[API] POST /api/conversations - 201 - 18ms',
  timestamp: '2025-08-05 01:02:02'
}
{
  requestId: 'e343f3b7-c967-4474-9f6e-a9e590d3f7a5',
  method: 'POST',
  url: '/api/conversations',
  ip: '::1',
  userAgent: 'axios/1.11.0',
  userId: undefined,
  level: 'http',
  message: 'Request started: POST /api/conversations',
  timestamp: '2025-08-05 01:02:02'
}
{
  currentUserId: '68912dd9ef87f5d380f7aa4d',
  participantId: '68912dd9ef87f5d380f7aa51',
  category: 'conversation',
  level: 'info',
  message: '[CONVERSATION] Create conversation attempt',
  timestamp: '2025-08-05 01:02:02'
}
{
  conversationId: new ObjectId('68912ddaef87f5d380f7aa57'),
  participants: [ '68912dd9ef87f5d380f7aa4d', '68912dd9ef87f5d380f7aa51' ],
  category: 'conversation',
  level: 'info',
  message: 'Existing conversation found',
  timestamp: '2025-08-05 01:02:02'
}
{
  conversationId: new ObjectId('68912ddaef87f5d380f7aa57'),
  currentUserId: '68912dd9ef87f5d380f7aa4d',
  participantId: '68912dd9ef87f5d380f7aa51',
  isNew: true,
  category: 'conversation',
  level: 'info',
  message: '[CONVERSATION] Create conversation successful',
  timestamp: '2025-08-05 01:02:02'
}
{
  method: 'POST',
  url: '/api/conversations',
  ip: '::1',
  userAgent: 'axios/1.11.0',
  statusCode: 201,
  responseTime: 7,
  userId: '68912dd9ef87f5d380f7aa4d',
  category: 'api',
  level: 'http',
  message: '[API] POST /api/conversations - 201 - 7ms',
  timestamp: '2025-08-05 01:02:02'
}
{
  requestId: '1ed747ac-079e-4109-a04d-eab79de11fcd',
  method: 'GET',
  url: '/api/conversations',
  ip: '::1',
  userAgent: 'axios/1.11.0',
  userId: undefined,
  level: 'http',
  message: 'Request started: GET /api/conversations',
  timestamp: '2025-08-05 01:02:02'
}
{
  userId: '68912dd9ef87f5d380f7aa4d',
  page: 1,
  limit: 20,
  search: undefined,
  category: 'conversation',
  level: 'info',
  message: '[CONVERSATION] Get conversations attempt',
  timestamp: '2025-08-05 01:02:02'
}
{
  userId: '68912dd9ef87f5d380f7aa4d',
  conversationCount: 1,
  category: 'conversation',
  level: 'info',
  message: 'User conversations retrieved',
  timestamp: '2025-08-05 01:02:02'
}
{
  userId: '68912dd9ef87f5d380f7aa4d',
  totalConversations: 1,
  filteredCount: 1,
  returnedCount: 1,
  page: 1,
  limit: 20,
  category: 'conversation',
  level: 'info',
  message: '[CONVERSATION] Get conversations successful',
  timestamp: '2025-08-05 01:02:02'
}
{
  method: 'GET',
  url: '/api/conversations',
  ip: '::1',
  userAgent: 'axios/1.11.0',
  statusCode: 200,
  responseTime: 5,
  userId: '68912dd9ef87f5d380f7aa4d',
  category: 'api',
  level: 'http',
  message: '[API] GET /api/conversations - 200 - 5ms',
  timestamp: '2025-08-05 01:02:02'
}
{
  requestId: 'b3587bc8-e928-4eea-bc41-f69714f5bde7',
  method: 'GET',
  url: '/api/conversations',
  ip: '::1',
  userAgent: 'axios/1.11.0',
  userId: undefined,
  level: 'http',
  message: 'Request started: GET /api/conversations',
  timestamp: '2025-08-05 01:02:02'
}
{
  userId: '68912dd9ef87f5d380f7aa51',
  page: 1,
  limit: 20,
  search: undefined,
  category: 'conversation',
  level: 'info',
  message: '[CONVERSATION] Get conversations attempt',
  timestamp: '2025-08-05 01:02:02'
}
{
  userId: '68912dd9ef87f5d380f7aa51',
  conversationCount: 1,
  category: 'conversation',
  level: 'info',
  message: 'User conversations retrieved',
  timestamp: '2025-08-05 01:02:02'
}
{
  userId: '68912dd9ef87f5d380f7aa51',
  totalConversations: 1,
  filteredCount: 1,
  returnedCount: 1,
  page: 1,
  limit: 20,
  category: 'conversation',
  level: 'info',
  message: '[CONVERSATION] Get conversations successful',
  timestamp: '2025-08-05 01:02:02'
}
{
  method: 'GET',
  url: '/api/conversations',
  ip: '::1',
  userAgent: 'axios/1.11.0',
  statusCode: 200,
  responseTime: 5,
  userId: '68912dd9ef87f5d380f7aa51',
  category: 'api',
  level: 'http',
  message: '[API] GET /api/conversations - 200 - 5ms',
  timestamp: '2025-08-05 01:02:02'
}
{
  requestId: '0257638d-cca5-4255-ac75-a36154c8cd38',
  method: 'GET',
  url: '/api/conversations/68912ddaef87f5d380f7aa57',
  ip: '::1',
  userAgent: 'axios/1.11.0',
  userId: undefined,
  level: 'http',
  message: 'Request started: GET /api/conversations/68912ddaef87f5d380f7aa57',
  timestamp: '2025-08-05 01:02:02'
}
{
  conversationId: '68912ddaef87f5d380f7aa57',
  userId: '68912dd9ef87f5d380f7aa4d',
  category: 'conversation',
  level: 'info',
  message: '[CONVERSATION] Get conversation attempt',
  timestamp: '2025-08-05 01:02:02'
}
{
  conversationId: '68912ddaef87f5d380f7aa57',
  userId: '68912dd9ef87f5d380f7aa4d',
  error: 'Access denied: You are not a participant in this conversation',
  stack: 'Error: Access denied: You are not a participant in this conversation\n' +
    '    at ConversationService.getConversationById (/Users/<USER>/Desktop/njback/dist/services/conversation.service.js:114:23)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async getConversation (/Users/<USER>/Desktop/njback/dist/api/controllers/conversation.controller.js:114:34)',
  level: 'error',
  message: 'Failed to get conversation by ID',
  timestamp: '2025-08-05 01:02:02'
}
{
  message: 'Sentry DSN not configured, error tracking disabled',
  level: 'warn',
  timestamp: '2025-08-05 01:02:56'
}
{
  cors: {
    origin: 'http://localhost:3000',
    methods: [ 'GET', 'POST' ],
    credentials: true
  },
  pingTimeout: 60000,
  pingInterval: 25000,
  level: 'info',
  message: 'Socket.IO server created',
  timestamp: '2025-08-05 01:02:56'
}
{
  port: '3002',
  environment: 'development',
  healthCheck: 'http://localhost:3002/health',
  authAPI: 'http://localhost:3002/api/auth',
  socketIO: 'Socket.IO server initialized',
  level: 'info',
  message: '🚀 Server running on port 3002',
  timestamp: '2025-08-05 01:02:56'
}
{
  host: 'localhost',
  port: 6379,
  db: 0,
  level: 'info',
  message: 'Redis connected successfully',
  timestamp: '2025-08-05 01:02:56'
}
{
  level: 'info',
  message: 'Redis client ready',
  timestamp: '2025-08-05 01:02:56'
}
{
  level: 'info',
  message: 'Redis connection established',
  timestamp: '2025-08-05 01:02:56'
}
{
  level: 'info',
  message: 'Redis connected for Socket.IO service',
  timestamp: '2025-08-05 01:02:56'
}
{
  requestId: '416a5707-9cd5-4247-b77c-b280ab117027',
  method: 'POST',
  url: '/api/auth/register',
  ip: '::1',
  userAgent: 'axios/1.11.0',
  userId: undefined,
  level: 'http',
  message: 'Request started: POST /api/auth/register',
  timestamp: '2025-08-05 01:03:19'
}
{
  email: '<EMAIL>',
  username: 'testuser1_1754344999391',
  category: 'auth',
  level: 'info',
  message: '[AUTH] User registration attempt',
  timestamp: '2025-08-05 01:03:19'
}
{
  userId: '68912e27d306d81dcbf04f22',
  email: '<EMAIL>',
  username: 'testuser1_1754344999391',
  category: 'auth',
  level: 'info',
  message: '[AUTH] User registration successful',
  timestamp: '2025-08-05 01:03:19'
}
{
  method: 'POST',
  url: '/api/auth/register',
  ip: '::1',
  userAgent: 'axios/1.11.0',
  statusCode: 201,
  responseTime: 227,
  userId: undefined,
  category: 'api',
  level: 'http',
  message: '[API] POST /api/auth/register - 201 - 227ms',
  timestamp: '2025-08-05 01:03:19'
}
{
  requestId: 'b82ffd2e-aeea-4d36-a759-1b58bc4567f5',
  method: 'POST',
  url: '/api/auth/register',
  ip: '::1',
  userAgent: 'axios/1.11.0',
  userId: undefined,
  level: 'http',
  message: 'Request started: POST /api/auth/register',
  timestamp: '2025-08-05 01:03:19'
}
{
  email: '<EMAIL>',
  username: 'testuser2_1754344999391',
  category: 'auth',
  level: 'info',
  message: '[AUTH] User registration attempt',
  timestamp: '2025-08-05 01:03:19'
}
{
  userId: '68912e27d306d81dcbf04f26',
  email: '<EMAIL>',
  username: 'testuser2_1754344999391',
  category: 'auth',
  level: 'info',
  message: '[AUTH] User registration successful',
  timestamp: '2025-08-05 01:03:19'
}
{
  method: 'POST',
  url: '/api/auth/register',
  ip: '::1',
  userAgent: 'axios/1.11.0',
  statusCode: 201,
  responseTime: 208,
  userId: undefined,
  category: 'api',
  level: 'http',
  message: '[API] POST /api/auth/register - 201 - 208ms',
  timestamp: '2025-08-05 01:03:19'
}
{
  requestId: '8595ae0f-2e50-4661-87fc-7f6473f159a4',
  method: 'POST',
  url: '/api/auth/login',
  ip: '::1',
  userAgent: 'axios/1.11.0',
  userId: undefined,
  level: 'http',
  message: 'Request started: POST /api/auth/login',
  timestamp: '2025-08-05 01:03:19'
}
{
  email: '<EMAIL>',
  category: 'auth',
  level: 'info',
  message: '[AUTH] User login attempt',
  timestamp: '2025-08-05 01:03:19'
}
{
  userId: '68912e27d306d81dcbf04f22',
  email: '<EMAIL>',
  username: 'testuser1_1754344999391',
  category: 'auth',
  level: 'info',
  message: '[AUTH] User login successful',
  timestamp: '2025-08-05 01:03:20'
}
{
  method: 'POST',
  url: '/api/auth/login',
  ip: '::1',
  userAgent: 'axios/1.11.0',
  statusCode: 200,
  responseTime: 203,
  userId: undefined,
  category: 'api',
  level: 'http',
  message: '[API] POST /api/auth/login - 200 - 203ms',
  timestamp: '2025-08-05 01:03:20'
}
{
  requestId: 'f097767a-4e16-4d72-bfa5-065803268113',
  method: 'POST',
  url: '/api/auth/login',
  ip: '::1',
  userAgent: 'axios/1.11.0',
  userId: undefined,
  level: 'http',
  message: 'Request started: POST /api/auth/login',
  timestamp: '2025-08-05 01:03:20'
}
{
  email: '<EMAIL>',
  category: 'auth',
  level: 'info',
  message: '[AUTH] User login attempt',
  timestamp: '2025-08-05 01:03:20'
}
{
  userId: '68912e27d306d81dcbf04f26',
  email: '<EMAIL>',
  username: 'testuser2_1754344999391',
  category: 'auth',
  level: 'info',
  message: '[AUTH] User login successful',
  timestamp: '2025-08-05 01:03:20'
}
{
  method: 'POST',
  url: '/api/auth/login',
  ip: '::1',
  userAgent: 'axios/1.11.0',
  statusCode: 200,
  responseTime: 201,
  userId: undefined,
  category: 'api',
  level: 'http',
  message: '[API] POST /api/auth/login - 200 - 201ms',
  timestamp: '2025-08-05 01:03:20'
}
{
  requestId: '0f381c4a-745b-430d-be1c-962af1e4339b',
  method: 'POST',
  url: '/api/conversations',
  ip: '::1',
  userAgent: 'axios/1.11.0',
  userId: undefined,
  level: 'http',
  message: 'Request started: POST /api/conversations',
  timestamp: '2025-08-05 01:03:20'
}
{
  currentUserId: '68912e27d306d81dcbf04f22',
  participantId: '68912e27d306d81dcbf04f26',
  category: 'conversation',
  level: 'info',
  message: '[CONVERSATION] Create conversation attempt',
  timestamp: '2025-08-05 01:03:20'
}
{
  conversationId: new ObjectId('68912e28d306d81dcbf04f2c'),
  participants: [ '68912e27d306d81dcbf04f22', '68912e27d306d81dcbf04f26' ],
  category: 'conversation',
  level: 'info',
  message: 'New conversation created',
  timestamp: '2025-08-05 01:03:20'
}
{
  conversationId: new ObjectId('68912e28d306d81dcbf04f2c'),
  currentUserId: '68912e27d306d81dcbf04f22',
  participantId: '68912e27d306d81dcbf04f26',
  isNew: true,
  category: 'conversation',
  level: 'info',
  message: '[CONVERSATION] Create conversation successful',
  timestamp: '2025-08-05 01:03:20'
}
{
  method: 'POST',
  url: '/api/conversations',
  ip: '::1',
  userAgent: 'axios/1.11.0',
  statusCode: 201,
  responseTime: 15,
  userId: '68912e27d306d81dcbf04f22',
  category: 'api',
  level: 'http',
  message: '[API] POST /api/conversations - 201 - 15ms',
  timestamp: '2025-08-05 01:03:20'
}
{
  requestId: '65baa5ec-fa2d-4403-a83b-02a7625bb148',
  method: 'POST',
  url: '/api/conversations',
  ip: '::1',
  userAgent: 'axios/1.11.0',
  userId: undefined,
  level: 'http',
  message: 'Request started: POST /api/conversations',
  timestamp: '2025-08-05 01:03:20'
}
{
  currentUserId: '68912e27d306d81dcbf04f22',
  participantId: '68912e27d306d81dcbf04f26',
  category: 'conversation',
  level: 'info',
  message: '[CONVERSATION] Create conversation attempt',
  timestamp: '2025-08-05 01:03:20'
}
{
  conversationId: new ObjectId('68912e28d306d81dcbf04f2c'),
  participants: [ '68912e27d306d81dcbf04f22', '68912e27d306d81dcbf04f26' ],
  category: 'conversation',
  level: 'info',
  message: 'Existing conversation found',
  timestamp: '2025-08-05 01:03:20'
}
{
  conversationId: new ObjectId('68912e28d306d81dcbf04f2c'),
  currentUserId: '68912e27d306d81dcbf04f22',
  participantId: '68912e27d306d81dcbf04f26',
  isNew: true,
  category: 'conversation',
  level: 'info',
  message: '[CONVERSATION] Create conversation successful',
  timestamp: '2025-08-05 01:03:20'
}
{
  method: 'POST',
  url: '/api/conversations',
  ip: '::1',
  userAgent: 'axios/1.11.0',
  statusCode: 201,
  responseTime: 7,
  userId: '68912e27d306d81dcbf04f22',
  category: 'api',
  level: 'http',
  message: '[API] POST /api/conversations - 201 - 7ms',
  timestamp: '2025-08-05 01:03:20'
}
{
  requestId: '04e2ab09-b6f1-47a3-846e-cc4d895af6e8',
  method: 'GET',
  url: '/api/conversations',
  ip: '::1',
  userAgent: 'axios/1.11.0',
  userId: undefined,
  level: 'http',
  message: 'Request started: GET /api/conversations',
  timestamp: '2025-08-05 01:03:20'
}
{
  userId: '68912e27d306d81dcbf04f22',
  page: 1,
  limit: 20,
  search: undefined,
  category: 'conversation',
  level: 'info',
  message: '[CONVERSATION] Get conversations attempt',
  timestamp: '2025-08-05 01:03:20'
}
{
  userId: '68912e27d306d81dcbf04f22',
  conversationCount: 1,
  category: 'conversation',
  level: 'info',
  message: 'User conversations retrieved',
  timestamp: '2025-08-05 01:03:20'
}
{
  userId: '68912e27d306d81dcbf04f22',
  totalConversations: 1,
  filteredCount: 1,
  returnedCount: 1,
  page: 1,
  limit: 20,
  category: 'conversation',
  level: 'info',
  message: '[CONVERSATION] Get conversations successful',
  timestamp: '2025-08-05 01:03:20'
}
{
  method: 'GET',
  url: '/api/conversations',
  ip: '::1',
  userAgent: 'axios/1.11.0',
  statusCode: 200,
  responseTime: 6,
  userId: '68912e27d306d81dcbf04f22',
  category: 'api',
  level: 'http',
  message: '[API] GET /api/conversations - 200 - 6ms',
  timestamp: '2025-08-05 01:03:20'
}
{
  requestId: '1734cdd9-0828-427a-8b40-4da0f1f9d441',
  method: 'GET',
  url: '/api/conversations',
  ip: '::1',
  userAgent: 'axios/1.11.0',
  userId: undefined,
  level: 'http',
  message: 'Request started: GET /api/conversations',
  timestamp: '2025-08-05 01:03:20'
}
{
  userId: '68912e27d306d81dcbf04f26',
  page: 1,
  limit: 20,
  search: undefined,
  category: 'conversation',
  level: 'info',
  message: '[CONVERSATION] Get conversations attempt',
  timestamp: '2025-08-05 01:03:20'
}
{
  userId: '68912e27d306d81dcbf04f26',
  conversationCount: 1,
  category: 'conversation',
  level: 'info',
  message: 'User conversations retrieved',
  timestamp: '2025-08-05 01:03:20'
}
{
  userId: '68912e27d306d81dcbf04f26',
  totalConversations: 1,
  filteredCount: 1,
  returnedCount: 1,
  page: 1,
  limit: 20,
  category: 'conversation',
  level: 'info',
  message: '[CONVERSATION] Get conversations successful',
  timestamp: '2025-08-05 01:03:20'
}
{
  method: 'GET',
  url: '/api/conversations',
  ip: '::1',
  userAgent: 'axios/1.11.0',
  statusCode: 200,
  responseTime: 6,
  userId: '68912e27d306d81dcbf04f26',
  category: 'api',
  level: 'http',
  message: '[API] GET /api/conversations - 200 - 6ms',
  timestamp: '2025-08-05 01:03:20'
}
{
  requestId: 'ee2bef2f-04a3-46a4-8904-fb6e3992fb28',
  method: 'GET',
  url: '/api/conversations/68912e28d306d81dcbf04f2c',
  ip: '::1',
  userAgent: 'axios/1.11.0',
  userId: undefined,
  level: 'http',
  message: 'Request started: GET /api/conversations/68912e28d306d81dcbf04f2c',
  timestamp: '2025-08-05 01:03:20'
}
{
  conversationId: '68912e28d306d81dcbf04f2c',
  userId: '68912e27d306d81dcbf04f22',
  category: 'conversation',
  level: 'info',
  message: '[CONVERSATION] Get conversation attempt',
  timestamp: '2025-08-05 01:03:20'
}
{
  conversationId: '68912e28d306d81dcbf04f2c',
  userId: '68912e27d306d81dcbf04f22',
  category: 'conversation',
  level: 'info',
  message: '[CONVERSATION] Get conversation successful',
  timestamp: '2025-08-05 01:03:20'
}
{
  method: 'GET',
  url: '/api/conversations/68912e28d306d81dcbf04f2c',
  ip: '::1',
  userAgent: 'axios/1.11.0',
  statusCode: 200,
  responseTime: 6,
  userId: '68912e27d306d81dcbf04f22',
  category: 'api',
  level: 'http',
  message: '[API] GET /api/conversations/68912e28d306d81dcbf04f2c - 200 - 6ms',
  timestamp: '2025-08-05 01:03:20'
}
{
  requestId: 'c9f57162-f59d-4b79-84de-2d351a7a4012',
  method: 'GET',
  url: '/api/conversations?page=1&limit=5',
  ip: '::1',
  userAgent: 'axios/1.11.0',
  userId: undefined,
  level: 'http',
  message: 'Request started: GET /api/conversations?page=1&limit=5',
  timestamp: '2025-08-05 01:03:20'
}
{
  userId: '68912e27d306d81dcbf04f22',
  page: 1,
  limit: 5,
  search: undefined,
  category: 'conversation',
  level: 'info',
  message: '[CONVERSATION] Get conversations attempt',
  timestamp: '2025-08-05 01:03:20'
}
{
  userId: '68912e27d306d81dcbf04f22',
  conversationCount: 1,
  category: 'conversation',
  level: 'info',
  message: 'User conversations retrieved',
  timestamp: '2025-08-05 01:03:20'
}
{
  userId: '68912e27d306d81dcbf04f22',
  totalConversations: 1,
  filteredCount: 1,
  returnedCount: 1,
  page: 1,
  limit: 5,
  category: 'conversation',
  level: 'info',
  message: '[CONVERSATION] Get conversations successful',
  timestamp: '2025-08-05 01:03:20'
}
{
  method: 'GET',
  url: '/api/conversations?page=1&limit=5',
  ip: '::1',
  userAgent: 'axios/1.11.0',
  statusCode: 200,
  responseTime: 5,
  userId: '68912e27d306d81dcbf04f22',
  category: 'api',
  level: 'http',
  message: '[API] GET /api/conversations?page=1&limit=5 - 200 - 5ms',
  timestamp: '2025-08-05 01:03:20'
}
{
  requestId: '08b87544-ee30-48b9-a0b8-13542b505deb',
  method: 'GET',
  url: '/api/conversations?search=testuser2',
  ip: '::1',
  userAgent: 'axios/1.11.0',
  userId: undefined,
  level: 'http',
  message: 'Request started: GET /api/conversations?search=testuser2',
  timestamp: '2025-08-05 01:03:20'
}
{
  userId: '68912e27d306d81dcbf04f22',
  page: 1,
  limit: 20,
  search: 'testuser2',
  category: 'conversation',
  level: 'info',
  message: '[CONVERSATION] Get conversations attempt',
  timestamp: '2025-08-05 01:03:20'
}
{
  userId: '68912e27d306d81dcbf04f22',
  conversationCount: 1,
  category: 'conversation',
  level: 'info',
  message: 'User conversations retrieved',
  timestamp: '2025-08-05 01:03:20'
}
{
  userId: '68912e27d306d81dcbf04f22',
  totalConversations: 1,
  filteredCount: 1,
  returnedCount: 1,
  page: 1,
  limit: 20,
  category: 'conversation',
  level: 'info',
  message: '[CONVERSATION] Get conversations successful',
  timestamp: '2025-08-05 01:03:20'
}
{
  method: 'GET',
  url: '/api/conversations?search=testuser2',
  ip: '::1',
  userAgent: 'axios/1.11.0',
  statusCode: 200,
  responseTime: 4,
  userId: '68912e27d306d81dcbf04f22',
  category: 'api',
  level: 'http',
  message: '[API] GET /api/conversations?search=testuser2 - 200 - 4ms',
  timestamp: '2025-08-05 01:03:20'
}
{
  requestId: 'd941024b-9223-4f93-9819-cbfe8b4fc6bd',
  method: 'POST',
  url: '/api/conversations',
  ip: '::1',
  userAgent: 'axios/1.11.0',
  userId: undefined,
  level: 'http',
  message: 'Request started: POST /api/conversations',
  timestamp: '2025-08-05 01:03:20'
}
{
  currentUserId: '68912e27d306d81dcbf04f22',
  participantId: '507f1f77bcf86cd799439011',
  category: 'conversation',
  level: 'info',
  message: '[CONVERSATION] Create conversation attempt',
  timestamp: '2025-08-05 01:03:20'
}
{
  currentUserId: '68912e27d306d81dcbf04f22',
  participantId: '507f1f77bcf86cd799439011',
  error: 'Participant user not found',
  stack: 'Error: Participant user not found\n' +
    '    at ConversationService.createOrFindConversation (/Users/<USER>/Desktop/njback/dist/services/conversation.service.js:26:23)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async createConversation (/Users/<USER>/Desktop/njback/dist/api/controllers/conversation.controller.js:19:34)',
  level: 'error',
  message: 'Failed to create or find conversation',
  timestamp: '2025-08-05 01:03:20'
}
{
  message: 'Sentry DSN not configured, error tracking disabled',
  level: 'warn',
  timestamp: '2025-08-05 01:04:43'
}
{
  cors: {
    origin: 'http://localhost:3000',
    methods: [ 'GET', 'POST' ],
    credentials: true
  },
  pingTimeout: 60000,
  pingInterval: 25000,
  level: 'info',
  message: 'Socket.IO server created',
  timestamp: '2025-08-05 01:04:43'
}
{
  port: '3002',
  environment: 'development',
  healthCheck: 'http://localhost:3002/health',
  authAPI: 'http://localhost:3002/api/auth',
  socketIO: 'Socket.IO server initialized',
  level: 'info',
  message: '🚀 Server running on port 3002',
  timestamp: '2025-08-05 01:04:43'
}
{
  host: 'localhost',
  port: 6379,
  db: 0,
  level: 'info',
  message: 'Redis connected successfully',
  timestamp: '2025-08-05 01:04:43'
}
{
  level: 'info',
  message: 'Redis client ready',
  timestamp: '2025-08-05 01:04:43'
}
{
  level: 'info',
  message: 'Redis connection established',
  timestamp: '2025-08-05 01:04:43'
}
{
  level: 'info',
  message: 'Redis connected for Socket.IO service',
  timestamp: '2025-08-05 01:04:43'
}
{
  requestId: '64ed8299-8522-4c45-8b54-ba76d8b1bd0e',
  method: 'GET',
  url: '/health',
  ip: '::1',
  userAgent: 'curl/8.7.1',
  userId: undefined,
  level: 'http',
  message: 'Request started: GET /health',
  timestamp: '2025-08-05 01:05:04'
}
{
  method: 'GET',
  url: '/health',
  ip: '::1',
  userAgent: 'curl/8.7.1',
  statusCode: 200,
  responseTime: 2,
  userId: undefined,
  category: 'api',
  level: 'http',
  message: '[API] GET /health - 200 - 2ms',
  timestamp: '2025-08-05 01:05:04'
}
{
  requestId: '34a8d2fa-9e76-461f-a802-f9702cb1684e',
  method: 'POST',
  url: '/api/auth/register',
  ip: '::1',
  userAgent: 'curl/8.7.1',
  userId: undefined,
  level: 'http',
  message: 'Request started: POST /api/auth/register',
  timestamp: '2025-08-05 01:05:30'
}
{
  email: '<EMAIL>',
  username: 'testuser1',
  category: 'auth',
  level: 'info',
  message: '[AUTH] User registration attempt',
  timestamp: '2025-08-05 01:05:30'
}
{
  userId: '68912eaa02bba617f673fbe2',
  email: '<EMAIL>',
  username: 'testuser1',
  category: 'auth',
  level: 'info',
  message: '[AUTH] User registration successful',
  timestamp: '2025-08-05 01:05:30'
}
{
  method: 'POST',
  url: '/api/auth/register',
  ip: '::1',
  userAgent: 'curl/8.7.1',
  statusCode: 201,
  responseTime: 231,
  userId: undefined,
  category: 'api',
  level: 'http',
  message: '[API] POST /api/auth/register - 201 - 231ms',
  timestamp: '2025-08-05 01:05:30'
}
{
  requestId: '76acbde1-08c5-4f9d-9d41-b2d888ff9db4',
  method: 'POST',
  url: '/api/auth/register',
  ip: '::1',
  userAgent: 'curl/8.7.1',
  userId: undefined,
  level: 'http',
  message: 'Request started: POST /api/auth/register',
  timestamp: '2025-08-05 01:06:04'
}
{
  email: '<EMAIL>',
  username: 'testuser2',
  category: 'auth',
  level: 'info',
  message: '[AUTH] User registration attempt',
  timestamp: '2025-08-05 01:06:04'
}
{
  userId: '68912ecc02bba617f673fbe6',
  email: '<EMAIL>',
  username: 'testuser2',
  category: 'auth',
  level: 'info',
  message: '[AUTH] User registration successful',
  timestamp: '2025-08-05 01:06:04'
}
{
  method: 'POST',
  url: '/api/auth/register',
  ip: '::1',
  userAgent: 'curl/8.7.1',
  statusCode: 201,
  responseTime: 208,
  userId: undefined,
  category: 'api',
  level: 'http',
  message: '[API] POST /api/auth/register - 201 - 208ms',
  timestamp: '2025-08-05 01:06:04'
}
{
  requestId: '36cf9245-1d87-43b3-913c-cb5abbee2b30',
  method: 'POST',
  url: '/api/conversations',
  ip: '::1',
  userAgent: 'curl/8.7.1',
  userId: undefined,
  level: 'http',
  message: 'Request started: POST /api/conversations',
  timestamp: '2025-08-05 01:06:31'
}
{
  currentUserId: '68912eaa02bba617f673fbe2',
  participantId: '68912ecc02bba617f673fbe6',
  category: 'conversation',
  level: 'info',
  message: '[CONVERSATION] Create conversation attempt',
  timestamp: '2025-08-05 01:06:31'
}
{
  conversationId: new ObjectId('68912ee702bba617f673fbea'),
  participants: [ '68912eaa02bba617f673fbe2', '68912ecc02bba617f673fbe6' ],
  category: 'conversation',
  level: 'info',
  message: 'New conversation created',
  timestamp: '2025-08-05 01:06:31'
}
{
  conversationId: new ObjectId('68912ee702bba617f673fbea'),
  currentUserId: '68912eaa02bba617f673fbe2',
  participantId: '68912ecc02bba617f673fbe6',
  isNew: true,
  category: 'conversation',
  level: 'info',
  message: '[CONVERSATION] Create conversation successful',
  timestamp: '2025-08-05 01:06:31'
}
{
  method: 'POST',
  url: '/api/conversations',
  ip: '::1',
  userAgent: 'curl/8.7.1',
  statusCode: 201,
  responseTime: 15,
  userId: '68912eaa02bba617f673fbe2',
  category: 'api',
  level: 'http',
  message: '[API] POST /api/conversations - 201 - 15ms',
  timestamp: '2025-08-05 01:06:31'
}
{
  requestId: '37c77f3d-c0c1-4ed1-b9a9-38242ea2e778',
  method: 'GET',
  url: '/api/conversations',
  ip: '::1',
  userAgent: 'curl/8.7.1',
  userId: undefined,
  level: 'http',
  message: 'Request started: GET /api/conversations',
  timestamp: '2025-08-05 01:06:54'
}
{
  userId: '68912eaa02bba617f673fbe2',
  page: 1,
  limit: 20,
  search: undefined,
  category: 'conversation',
  level: 'info',
  message: '[CONVERSATION] Get conversations attempt',
  timestamp: '2025-08-05 01:06:54'
}
{
  userId: '68912eaa02bba617f673fbe2',
  conversationCount: 1,
  category: 'conversation',
  level: 'info',
  message: 'User conversations retrieved',
  timestamp: '2025-08-05 01:06:54'
}
{
  userId: '68912eaa02bba617f673fbe2',
  totalConversations: 1,
  filteredCount: 1,
  returnedCount: 1,
  page: 1,
  limit: 20,
  category: 'conversation',
  level: 'info',
  message: '[CONVERSATION] Get conversations successful',
  timestamp: '2025-08-05 01:06:54'
}
{
  method: 'GET',
  url: '/api/conversations',
  ip: '::1',
  userAgent: 'curl/8.7.1',
  statusCode: 200,
  responseTime: 8,
  userId: '68912eaa02bba617f673fbe2',
  category: 'api',
  level: 'http',
  message: '[API] GET /api/conversations - 200 - 8ms',
  timestamp: '2025-08-05 01:06:54'
}
{
  requestId: '938ba97f-eab1-4604-ba9f-b3629e55c99a',
  method: 'GET',
  url: '/api/conversations/68912ee702bba617f673fbea',
  ip: '::1',
  userAgent: 'curl/8.7.1',
  userId: undefined,
  level: 'http',
  message: 'Request started: GET /api/conversations/68912ee702bba617f673fbea',
  timestamp: '2025-08-05 01:07:17'
}
{
  conversationId: '68912ee702bba617f673fbea',
  userId: '68912eaa02bba617f673fbe2',
  category: 'conversation',
  level: 'info',
  message: '[CONVERSATION] Get conversation attempt',
  timestamp: '2025-08-05 01:07:17'
}
{
  conversationId: '68912ee702bba617f673fbea',
  userId: '68912eaa02bba617f673fbe2',
  category: 'conversation',
  level: 'info',
  message: '[CONVERSATION] Get conversation successful',
  timestamp: '2025-08-05 01:07:17'
}
{
  method: 'GET',
  url: '/api/conversations/68912ee702bba617f673fbea',
  ip: '::1',
  userAgent: 'curl/8.7.1',
  statusCode: 200,
  responseTime: 7,
  userId: '68912eaa02bba617f673fbe2',
  category: 'api',
  level: 'http',
  message: '[API] GET /api/conversations/68912ee702bba617f673fbea - 200 - 7ms',
  timestamp: '2025-08-05 01:07:17'
}
{
  requestId: '52e6729e-e793-4b9d-b1bc-774248628ad2',
  method: 'DELETE',
  url: '/api/conversations/68912ee702bba617f673fbea',
  ip: '::1',
  userAgent: 'curl/8.7.1',
  userId: undefined,
  level: 'http',
  message: 'Request started: DELETE /api/conversations/68912ee702bba617f673fbea',
  timestamp: '2025-08-05 01:07:40'
}
{
  conversationId: '68912ee702bba617f673fbea',
  userId: '68912eaa02bba617f673fbe2',
  category: 'conversation',
  level: 'info',
  message: '[CONVERSATION] Delete conversation attempt',
  timestamp: '2025-08-05 01:07:40'
}
{
  conversationId: '68912ee702bba617f673fbea',
  userId: '68912eaa02bba617f673fbe2',
  category: 'conversation',
  level: 'info',
  message: 'Conversation deleted',
  timestamp: '2025-08-05 01:07:40'
}
{
  conversationId: '68912ee702bba617f673fbea',
  userId: '68912eaa02bba617f673fbe2',
  category: 'conversation',
  level: 'info',
  message: '[CONVERSATION] Delete conversation successful',
  timestamp: '2025-08-05 01:07:40'
}
{
  method: 'DELETE',
  url: '/api/conversations/68912ee702bba617f673fbea',
  ip: '::1',
  userAgent: 'curl/8.7.1',
  statusCode: 200,
  responseTime: 6,
  userId: '68912eaa02bba617f673fbe2',
  category: 'api',
  level: 'http',
  message: '[API] DELETE /api/conversations/68912ee702bba617f673fbea - 200 - 6ms',
  timestamp: '2025-08-05 01:07:40'
}
{
  message: 'Sentry DSN not configured, error tracking disabled',
  level: 'warn',
  timestamp: '2025-08-05 09:01:41'
}
{
  cors: 'http://localhost:3000',
  category: 'socket',
  level: 'info',
  message: '[SOCKET] Socket.IO server initialized',
  timestamp: '2025-08-05 09:01:41'
}
{
  port: '3002',
  environment: 'development',
  healthCheck: 'http://localhost:3002/health',
  authAPI: 'http://localhost:3002/api/auth',
  socketIO: 'Socket.IO server initialized',
  level: 'info',
  message: '🚀 Server running on port 3002',
  timestamp: '2025-08-05 09:01:41'
}
{
  requestId: '022791a3-cd1b-4886-9670-c1963a4e317b',
  method: 'POST',
  url: '/api/auth/login',
  ip: '::1',
  userAgent: 'node',
  userId: undefined,
  level: 'http',
  message: 'Request started: POST /api/auth/login',
  timestamp: '2025-08-05 09:02:38'
}
{
  email: '<EMAIL>',
  category: 'auth',
  level: 'info',
  message: '[AUTH] User login attempt',
  timestamp: '2025-08-05 09:02:38'
}
{
  email: '<EMAIL>',
  userId: '6891106a6280acb97804e1d6',
  category: 'security',
  level: 'warn',
  message: '[SECURITY] Login failed - invalid password',
  timestamp: '2025-08-05 09:02:38'
}
{
  requestId: '022791a3-cd1b-4886-9670-c1963a4e317b',
  method: 'POST',
  url: '/api/auth/login',
  ip: '::1',
  userAgent: 'node',
  userId: undefined,
  responseTime: 216,
  body: { email: '<EMAIL>', password: '[REDACTED]' },
  query: {},
  params: {},
  error: 'Invalid email or password',
  stack: 'Error: Invalid email or password\n' +
    '    at AuthService.login (/Users/<USER>/Desktop/njback/src/services/auth.service.ts:158:15)\n' +
    '    at AuthController.login (/Users/<USER>/Desktop/njback/src/api/controllers/auth.controller.ts:33:22)',
  level: 'error',
  message: 'Request failed: POST /api/auth/login',
  timestamp: '2025-08-05 09:02:38'
}
{
  error: 'Invalid email or password',
  stack: 'Error: Invalid email or password\n' +
    '    at AuthService.login (/Users/<USER>/Desktop/njback/src/services/auth.service.ts:158:15)\n' +
    '    at AuthController.login (/Users/<USER>/Desktop/njback/src/api/controllers/auth.controller.ts:33:22)',
  context: {
    requestId: '022791a3-cd1b-4886-9670-c1963a4e317b',
    method: 'POST',
    url: '/api/auth/login',
    ip: '::1',
    userAgent: 'node',
    userId: undefined,
    responseTime: 216,
    body: { email: '<EMAIL>', password: '[REDACTED]' },
    query: {},
    params: {}
  },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 09:02:38'
}
{
  method: 'POST',
  url: '/api/auth/login',
  ip: '::1',
  userAgent: 'node',
  statusCode: 401,
  responseTime: 219,
  userId: undefined,
  category: 'api',
  level: 'error',
  message: '[API] POST /api/auth/login - 401 - 219ms',
  timestamp: '2025-08-05 09:02:38'
}
{
  requestId: '651b89e6-9b9f-4533-b855-aae28dee2e7a',
  method: 'POST',
  url: '/api/auth/register',
  ip: '::1',
  userAgent: 'node',
  userId: undefined,
  level: 'http',
  message: 'Request started: POST /api/auth/register',
  timestamp: '2025-08-05 09:02:38'
}
{
  requestId: '651b89e6-9b9f-4533-b855-aae28dee2e7a',
  method: 'POST',
  url: '/api/auth/register',
  ip: '::1',
  userAgent: 'node',
  userId: undefined,
  responseTime: 2,
  body: {
    username: 'testuser',
    email: '<EMAIL>',
    password: '[REDACTED]'
  },
  query: {},
  params: {},
  error: 'Validation failed',
  stack: 'Error: Validation failed\n' +
    '    at /Users/<USER>/Desktop/njback/src/api/middlewares/validation.middleware.ts:24:15\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:105:5)',
  level: 'error',
  message: 'Request failed: POST /api/auth/register',
  timestamp: '2025-08-05 09:02:38'
}
{
  error: 'Validation failed',
  stack: 'Error: Validation failed\n' +
    '    at /Users/<USER>/Desktop/njback/src/api/middlewares/validation.middleware.ts:24:15\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:105:5)',
  context: {
    requestId: '651b89e6-9b9f-4533-b855-aae28dee2e7a',
    method: 'POST',
    url: '/api/auth/register',
    ip: '::1',
    userAgent: 'node',
    userId: undefined,
    responseTime: 2,
    body: {
      username: 'testuser',
      email: '<EMAIL>',
      password: '[REDACTED]'
    },
    query: {},
    params: {}
  },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 09:02:38'
}
{
  method: 'POST',
  url: '/api/auth/register',
  ip: '::1',
  userAgent: 'node',
  statusCode: 400,
  responseTime: 2,
  userId: undefined,
  category: 'api',
  level: 'error',
  message: '[API] POST /api/auth/register - 400 - 2ms',
  timestamp: '2025-08-05 09:02:38'
}
{
  requestId: '7068f7a4-96c9-4d63-a5ce-d49dbd3fb66d',
  method: 'POST',
  url: '/api/auth/login',
  ip: '::1',
  userAgent: 'node',
  userId: undefined,
  level: 'http',
  message: 'Request started: POST /api/auth/login',
  timestamp: '2025-08-05 09:03:55'
}
{
  email: '<EMAIL>',
  category: 'auth',
  level: 'info',
  message: '[AUTH] User login attempt',
  timestamp: '2025-08-05 09:03:55'
}
{
  email: '<EMAIL>',
  userId: '6891106a6280acb97804e1d6',
  category: 'security',
  level: 'warn',
  message: '[SECURITY] Login failed - invalid password',
  timestamp: '2025-08-05 09:03:55'
}
{
  requestId: '7068f7a4-96c9-4d63-a5ce-d49dbd3fb66d',
  method: 'POST',
  url: '/api/auth/login',
  ip: '::1',
  userAgent: 'node',
  userId: undefined,
  responseTime: 207,
  body: { email: '<EMAIL>', password: '[REDACTED]' },
  query: {},
  params: {},
  error: 'Invalid email or password',
  stack: 'Error: Invalid email or password\n' +
    '    at AuthService.login (/Users/<USER>/Desktop/njback/src/services/auth.service.ts:158:15)\n' +
    '    at AuthController.login (/Users/<USER>/Desktop/njback/src/api/controllers/auth.controller.ts:33:22)',
  level: 'error',
  message: 'Request failed: POST /api/auth/login',
  timestamp: '2025-08-05 09:03:55'
}
{
  error: 'Invalid email or password',
  stack: 'Error: Invalid email or password\n' +
    '    at AuthService.login (/Users/<USER>/Desktop/njback/src/services/auth.service.ts:158:15)\n' +
    '    at AuthController.login (/Users/<USER>/Desktop/njback/src/api/controllers/auth.controller.ts:33:22)',
  context: {
    requestId: '7068f7a4-96c9-4d63-a5ce-d49dbd3fb66d',
    method: 'POST',
    url: '/api/auth/login',
    ip: '::1',
    userAgent: 'node',
    userId: undefined,
    responseTime: 207,
    body: { email: '<EMAIL>', password: '[REDACTED]' },
    query: {},
    params: {}
  },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 09:03:55'
}
{
  method: 'POST',
  url: '/api/auth/login',
  ip: '::1',
  userAgent: 'node',
  statusCode: 401,
  responseTime: 208,
  userId: undefined,
  category: 'api',
  level: 'error',
  message: '[API] POST /api/auth/login - 401 - 208ms',
  timestamp: '2025-08-05 09:03:55'
}
{
  requestId: '3195f1ef-2450-40a5-b310-61c35de1e3ee',
  method: 'POST',
  url: '/api/auth/register',
  ip: '::1',
  userAgent: 'node',
  userId: undefined,
  level: 'http',
  message: 'Request started: POST /api/auth/register',
  timestamp: '2025-08-05 09:03:55'
}
{
  requestId: '3195f1ef-2450-40a5-b310-61c35de1e3ee',
  method: 'POST',
  url: '/api/auth/register',
  ip: '::1',
  userAgent: 'node',
  userId: undefined,
  responseTime: 1,
  body: {
    username: 'testuser',
    email: '<EMAIL>',
    password: '[REDACTED]'
  },
  query: {},
  params: {},
  error: 'Validation failed',
  stack: 'Error: Validation failed\n' +
    '    at /Users/<USER>/Desktop/njback/src/api/middlewares/validation.middleware.ts:24:15\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:105:5)',
  level: 'error',
  message: 'Request failed: POST /api/auth/register',
  timestamp: '2025-08-05 09:03:55'
}
{
  error: 'Validation failed',
  stack: 'Error: Validation failed\n' +
    '    at /Users/<USER>/Desktop/njback/src/api/middlewares/validation.middleware.ts:24:15\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:105:5)',
  context: {
    requestId: '3195f1ef-2450-40a5-b310-61c35de1e3ee',
    method: 'POST',
    url: '/api/auth/register',
    ip: '::1',
    userAgent: 'node',
    userId: undefined,
    responseTime: 1,
    body: {
      username: 'testuser',
      email: '<EMAIL>',
      password: '[REDACTED]'
    },
    query: {},
    params: {}
  },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 09:03:55'
}
{
  method: 'POST',
  url: '/api/auth/register',
  ip: '::1',
  userAgent: 'node',
  statusCode: 400,
  responseTime: 3,
  userId: undefined,
  category: 'api',
  level: 'error',
  message: '[API] POST /api/auth/register - 400 - 3ms',
  timestamp: '2025-08-05 09:03:55'
}
{
  requestId: '82d33a46-562e-46c0-b473-9c68454a750c',
  method: 'POST',
  url: '/api/auth/login',
  ip: '::1',
  userAgent: 'node',
  userId: undefined,
  level: 'http',
  message: 'Request started: POST /api/auth/login',
  timestamp: '2025-08-05 09:04:43'
}
{
  email: '<EMAIL>',
  category: 'auth',
  level: 'info',
  message: '[AUTH] User login attempt',
  timestamp: '2025-08-05 09:04:43'
}
{
  userId: '6891106a6280acb97804e1d6',
  email: '<EMAIL>',
  username: 'testuser',
  category: 'auth',
  level: 'info',
  message: '[AUTH] User login successful',
  timestamp: '2025-08-05 09:04:43'
}
{
  method: 'POST',
  url: '/api/auth/login',
  ip: '::1',
  userAgent: 'node',
  statusCode: 200,
  responseTime: 206,
  userId: undefined,
  category: 'api',
  level: 'http',
  message: '[API] POST /api/auth/login - 200 - 206ms',
  timestamp: '2025-08-05 09:04:43'
}
{
  socketId: 'WCLnUbCDsjc6BQgXAAAB',
  ip: '::1',
  category: 'socket',
  level: 'warn',
  message: '[SOCKET] Authentication failed: No token provided',
  timestamp: '2025-08-05 09:04:43'
}
{
  socketId: 'AVJ98PhuuAKhEWTqAAAD',
  ip: '::1',
  category: 'socket',
  level: 'warn',
  message: '[SOCKET] Authentication failed: No token provided',
  timestamp: '2025-08-05 09:04:45'
}
{
  requestId: '2c4945a4-be84-4d0d-a04f-0b4ad9aced17',
  method: 'POST',
  url: '/api/auth/login',
  ip: '::1',
  userAgent: 'node',
  userId: undefined,
  level: 'http',
  message: 'Request started: POST /api/auth/login',
  timestamp: '2025-08-05 09:05:43'
}
{
  email: '<EMAIL>',
  category: 'auth',
  level: 'info',
  message: '[AUTH] User login attempt',
  timestamp: '2025-08-05 09:05:43'
}
{
  userId: '6891106a6280acb97804e1d6',
  email: '<EMAIL>',
  username: 'testuser',
  category: 'auth',
  level: 'info',
  message: '[AUTH] User login successful',
  timestamp: '2025-08-05 09:05:44'
}
{
  method: 'POST',
  url: '/api/auth/login',
  ip: '::1',
  userAgent: 'node',
  statusCode: 200,
  responseTime: 211,
  userId: undefined,
  category: 'api',
  level: 'http',
  message: '[API] POST /api/auth/login - 200 - 211ms',
  timestamp: '2025-08-05 09:05:44'
}
{
  requestId: 'e46455d8-a4f5-4b01-b613-24ad647cb87e',
  method: 'POST',
  url: '/api/auth/login',
  ip: '::1',
  userAgent: 'node',
  userId: undefined,
  level: 'http',
  message: 'Request started: POST /api/auth/login',
  timestamp: '2025-08-05 09:06:18'
}
{
  email: '<EMAIL>',
  category: 'auth',
  level: 'info',
  message: '[AUTH] User login attempt',
  timestamp: '2025-08-05 09:06:18'
}
{
  userId: '6891106a6280acb97804e1d6',
  email: '<EMAIL>',
  username: 'testuser',
  category: 'auth',
  level: 'info',
  message: '[AUTH] User login successful',
  timestamp: '2025-08-05 09:06:18'
}
{
  method: 'POST',
  url: '/api/auth/login',
  ip: '::1',
  userAgent: 'node',
  statusCode: 200,
  responseTime: 215,
  userId: undefined,
  category: 'api',
  level: 'http',
  message: '[API] POST /api/auth/login - 200 - 215ms',
  timestamp: '2025-08-05 09:06:18'
}
{
  requestId: '3a6b446d-02ef-41e5-9f76-f6915c288707',
  method: 'POST',
  url: '/api/auth/login',
  ip: '::1',
  userAgent: 'node',
  userId: undefined,
  level: 'http',
  message: 'Request started: POST /api/auth/login',
  timestamp: '2025-08-05 09:06:48'
}
{
  email: '<EMAIL>',
  category: 'auth',
  level: 'info',
  message: '[AUTH] User login attempt',
  timestamp: '2025-08-05 09:06:48'
}
{
  userId: '6891106a6280acb97804e1d6',
  email: '<EMAIL>',
  username: 'testuser',
  category: 'auth',
  level: 'info',
  message: '[AUTH] User login successful',
  timestamp: '2025-08-05 09:06:48'
}
{
  method: 'POST',
  url: '/api/auth/login',
  ip: '::1',
  userAgent: 'node',
  statusCode: 200,
  responseTime: 207,
  userId: undefined,
  category: 'api',
  level: 'http',
  message: '[API] POST /api/auth/login - 200 - 207ms',
  timestamp: '2025-08-05 09:06:48'
}
{
  socketId: 'ZwKZRUbvk1GJKM2MAAAF',
  ip: '::1',
  category: 'socket',
  level: 'warn',
  message: '[SOCKET] Authentication failed: No token provided',
  timestamp: '2025-08-05 09:06:48'
}
{
  socketId: 'R_7_REze4FOwOTqCAAAH',
  userId: '6891106a6280acb97804e1d6',
  username: 'testuser',
  ip: '::1',
  category: 'socket',
  level: 'info',
  message: '[SOCKET] User authenticated successfully',
  timestamp: '2025-08-05 09:06:50'
}
{
  socketId: 'R_7_REze4FOwOTqCAAAH',
  userId: '6891106a6280acb97804e1d6',
  username: 'testuser',
  totalConnections: 1,
  category: 'socket',
  level: 'info',
  message: '[SOCKET] User connected',
  timestamp: '2025-08-05 09:06:50'
}
{
  userId: '6891106a6280acb97804e1d6',
  username: 'testuser',
  status: 'online',
  category: 'socket',
  level: 'info',
  message: '[SOCKET] User status broadcasted: online',
  timestamp: '2025-08-05 09:06:50'
}
{
  socketId: 'R_7_REze4FOwOTqCAAAH',
  userId: '6891106a6280acb97804e1d6',
  conversationId: 'invalid-conversation-id',
  category: 'socket',
  level: 'info',
  message: '[SOCKET] Join conversation attempt',
  timestamp: '2025-08-05 09:06:53'
}
{
  socketId: 'R_7_REze4FOwOTqCAAAH',
  userId: '6891106a6280acb97804e1d6',
  conversationId: 'invalid-conversation-id',
  category: 'socket',
  error: 'Cast to ObjectId failed for value "invalid-conversation-id" (type string) at path "_id" for model "Conversation"',
  stack: 'CastError: Cast to ObjectId failed for value "invalid-conversation-id" (type string) at path "_id" for model "Conversation"\n' +
    '    at SchemaObjectId.cast (/Users/<USER>/Desktop/njback/node_modules/mongoose/lib/schema/objectId.js:251:11)\n' +
    '    at SchemaObjectId.SchemaType.applySetters (/Users/<USER>/Desktop/njback/node_modules/mongoose/lib/schemaType.js:1258:12)\n' +
    '    at SchemaObjectId.SchemaType.castForQuery (/Users/<USER>/Desktop/njback/node_modules/mongoose/lib/schemaType.js:1680:17)\n' +
    '    at cast (/Users/<USER>/Desktop/njback/node_modules/mongoose/lib/cast.js:390:32)\n' +
    '    at model.Query.Query.cast (/Users/<USER>/Desktop/njback/node_modules/mongoose/lib/query.js:5055:12)\n' +
    '    at model.Query.Query._castConditions (/Users/<USER>/Desktop/njback/node_modules/mongoose/lib/query.js:2351:10)\n' +
    '    at model.Query._findOne (/Users/<USER>/Desktop/njback/node_modules/mongoose/lib/query.js:2674:8)\n' +
    '    at model.Query.exec (/Users/<USER>/Desktop/njback/node_modules/mongoose/lib/query.js:4604:80)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at SocketController.handleJoinConversation (/Users/<USER>/Desktop/njback/src/socket/socket.controller.ts:58:28)',
  level: 'error',
  message: '[SOCKET] Error joining conversation',
  timestamp: '2025-08-05 09:06:53'
}
{
  error: 'Cast to ObjectId failed for value "invalid-conversation-id" (type string) at path "_id" for model "Conversation"',
  stack: 'CastError: Cast to ObjectId failed for value "invalid-conversation-id" (type string) at path "_id" for model "Conversation"\n' +
    '    at SchemaObjectId.cast (/Users/<USER>/Desktop/njback/node_modules/mongoose/lib/schema/objectId.js:251:11)\n' +
    '    at SchemaObjectId.SchemaType.applySetters (/Users/<USER>/Desktop/njback/node_modules/mongoose/lib/schemaType.js:1258:12)\n' +
    '    at SchemaObjectId.SchemaType.castForQuery (/Users/<USER>/Desktop/njback/node_modules/mongoose/lib/schemaType.js:1680:17)\n' +
    '    at cast (/Users/<USER>/Desktop/njback/node_modules/mongoose/lib/cast.js:390:32)\n' +
    '    at model.Query.Query.cast (/Users/<USER>/Desktop/njback/node_modules/mongoose/lib/query.js:5055:12)\n' +
    '    at model.Query.Query._castConditions (/Users/<USER>/Desktop/njback/node_modules/mongoose/lib/query.js:2351:10)\n' +
    '    at model.Query._findOne (/Users/<USER>/Desktop/njback/node_modules/mongoose/lib/query.js:2674:8)\n' +
    '    at model.Query.exec (/Users/<USER>/Desktop/njback/node_modules/mongoose/lib/query.js:4604:80)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at SocketController.handleJoinConversation (/Users/<USER>/Desktop/njback/src/socket/socket.controller.ts:58:28)',
  context: {
    socketId: 'R_7_REze4FOwOTqCAAAH',
    userId: '6891106a6280acb97804e1d6',
    conversationId: 'invalid-conversation-id',
    category: 'socket'
  },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 09:06:53'
}
{
  socketId: 'R_7_REze4FOwOTqCAAAH',
  userId: '6891106a6280acb97804e1d6',
  username: 'testuser',
  conversationId: 'test-conversation-id',
  category: 'socket',
  level: 'debug',
  message: '[SOCKET] User started typing',
  timestamp: '2025-08-05 09:06:55'
}
{
  socketId: 'R_7_REze4FOwOTqCAAAH',
  userId: '6891106a6280acb97804e1d6',
  username: 'testuser',
  conversationId: 'test-conversation-id',
  category: 'socket',
  level: 'debug',
  message: '[SOCKET] User stopped typing',
  timestamp: '2025-08-05 09:06:56'
}
{
  socketId: 'R_7_REze4FOwOTqCAAAH',
  userId: '6891106a6280acb97804e1d6',
  username: 'testuser',
  reason: 'client namespace disconnect',
  totalConnections: 0,
  category: 'socket',
  level: 'info',
  message: '[SOCKET] User disconnected',
  timestamp: '2025-08-05 09:06:56'
}
{
  userId: '6891106a6280acb97804e1d6',
  username: 'testuser',
  status: 'offline',
  category: 'socket',
  level: 'info',
  message: '[SOCKET] User status broadcasted: offline',
  timestamp: '2025-08-05 09:06:56'
}
