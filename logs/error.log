{
  error: 'this.initializeRedis is not a function',
  stack: 'TypeError: this.initializeRedis is not a function\n' +
    '    at new SocketService (/Users/<USER>/Desktop/njback/src/services/socket.service.ts:19:10)\n' +
    '    at startServer (/Users/<USER>/Desktop/njback/src/server.ts:32:5)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:105:5)',
  level: 'error',
  message: 'Failed to start server',
  timestamp: '2025-08-05 00:31:35'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:31:48'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:31:48'
}
{
  error: 'Connection is closed.',
  stack: 'Error: Connection is closed.\n' +
    '    at EventEmitter.connectionCloseHandler (/Users/<USER>/Desktop/njback/node_modules/ioredis/built/Redis.js:208:28)\n' +
    '    at Object.onceWrapper (node:events:622:26)\n' +
    '    at EventEmitter.emit (node:events:519:35)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:85:11)',
  level: 'error',
  message: 'Failed to connect to Redis',
  timestamp: '2025-08-05 00:31:48'
}
{
  error: 'Connection is closed.',
  stack: 'Error: Connection is closed.\n' +
    '    at EventEmitter.connectionCloseHandler (/Users/<USER>/Desktop/njback/node_modules/ioredis/built/Redis.js:208:28)\n' +
    '    at Object.onceWrapper (node:events:622:26)\n' +
    '    at EventEmitter.emit (node:events:519:35)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:85:11)',
  context: undefined,
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:31:48'
}
{
  error: 'Connection is closed.',
  stack: 'Error: Connection is closed.\n' +
    '    at EventEmitter.connectionCloseHandler (/Users/<USER>/Desktop/njback/node_modules/ioredis/built/Redis.js:208:28)\n' +
    '    at Object.onceWrapper (node:events:622:26)\n' +
    '    at EventEmitter.emit (node:events:519:35)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:85:11)',
  level: 'error',
  message: 'Failed to connect Redis for Socket.IO service',
  timestamp: '2025-08-05 00:31:48'
}
{
  error: 'Connection is closed.',
  stack: 'Error: Connection is closed.\n' +
    '    at EventEmitter.connectionCloseHandler (/Users/<USER>/Desktop/njback/node_modules/ioredis/built/Redis.js:208:28)\n' +
    '    at Object.onceWrapper (node:events:622:26)\n' +
    '    at EventEmitter.emit (node:events:519:35)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:85:11)',
  context: undefined,
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:31:48'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:31:49'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:31:49'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:31:49'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:31:49'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:31:49'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:31:49'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:31:49'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:31:49'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:31:49'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:31:49'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:31:50'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:31:50'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:31:50'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:31:50'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:31:50'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:31:50'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:31:51'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:31:51'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:31:51'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:31:51'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:31:52'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:31:52'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:31:52'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:31:52'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:31:53'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:31:53'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:31:54'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:31:54'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:31:55'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:31:55'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:31:55'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:31:55'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:31:56'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:31:56'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:31:57'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:31:57'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:31:58'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:31:58'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:31:59'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:31:59'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:00'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:00'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:01'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:01'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:02'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:02'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:04'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:04'
}
{
  error: 'Connection is closed.',
  stack: 'Error: Connection is closed.\n' +
    '    at EventEmitter.connectionCloseHandler (/Users/<USER>/Desktop/njback/node_modules/ioredis/built/Redis.js:208:28)\n' +
    '    at Object.onceWrapper (node:events:622:26)\n' +
    '    at EventEmitter.emit (node:events:519:35)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:85:11)',
  level: 'error',
  message: 'Failed to connect to Redis',
  timestamp: '2025-08-05 00:32:04'
}
{
  error: 'Connection is closed.',
  stack: 'Error: Connection is closed.\n' +
    '    at EventEmitter.connectionCloseHandler (/Users/<USER>/Desktop/njback/node_modules/ioredis/built/Redis.js:208:28)\n' +
    '    at Object.onceWrapper (node:events:622:26)\n' +
    '    at EventEmitter.emit (node:events:519:35)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:85:11)',
  context: undefined,
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:04'
}
{
  error: 'Connection is closed.',
  stack: 'Error: Connection is closed.\n' +
    '    at EventEmitter.connectionCloseHandler (/Users/<USER>/Desktop/njback/node_modules/ioredis/built/Redis.js:208:28)\n' +
    '    at Object.onceWrapper (node:events:622:26)\n' +
    '    at EventEmitter.emit (node:events:519:35)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:85:11)',
  level: 'error',
  message: 'Failed to connect Redis for Socket.IO service',
  timestamp: '2025-08-05 00:32:04'
}
{
  error: 'Connection is closed.',
  stack: 'Error: Connection is closed.\n' +
    '    at EventEmitter.connectionCloseHandler (/Users/<USER>/Desktop/njback/node_modules/ioredis/built/Redis.js:208:28)\n' +
    '    at Object.onceWrapper (node:events:622:26)\n' +
    '    at EventEmitter.emit (node:events:519:35)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:85:11)',
  context: undefined,
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:04'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:04'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:04'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:04'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:04'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:04'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:04'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:04'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:04'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:04'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:04'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:05'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:05'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:05'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:05'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:05'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:05'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:06'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:06'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:06'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:06'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:07'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:07'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:08'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:08'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:08'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:08'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:09'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:09'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:10'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:10'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:11'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:11'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:11'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:11'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:12'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:12'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:13'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:13'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:14'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:14'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:15'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:15'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:16'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:16'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:17'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:17'
}
{
  error: 'Connection is closed.',
  stack: 'Error: Connection is closed.\n' +
    '    at EventEmitter.connectionCloseHandler (/Users/<USER>/Desktop/njback/node_modules/ioredis/built/Redis.js:208:28)\n' +
    '    at Object.onceWrapper (node:events:622:26)\n' +
    '    at EventEmitter.emit (node:events:519:35)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:85:11)',
  level: 'error',
  message: 'Failed to connect to Redis',
  timestamp: '2025-08-05 00:32:17'
}
{
  error: 'Connection is closed.',
  stack: 'Error: Connection is closed.\n' +
    '    at EventEmitter.connectionCloseHandler (/Users/<USER>/Desktop/njback/node_modules/ioredis/built/Redis.js:208:28)\n' +
    '    at Object.onceWrapper (node:events:622:26)\n' +
    '    at EventEmitter.emit (node:events:519:35)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:85:11)',
  context: undefined,
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:17'
}
{
  error: 'Connection is closed.',
  stack: 'Error: Connection is closed.\n' +
    '    at EventEmitter.connectionCloseHandler (/Users/<USER>/Desktop/njback/node_modules/ioredis/built/Redis.js:208:28)\n' +
    '    at Object.onceWrapper (node:events:622:26)\n' +
    '    at EventEmitter.emit (node:events:519:35)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:85:11)',
  level: 'error',
  message: 'Failed to connect Redis for Socket.IO service',
  timestamp: '2025-08-05 00:32:17'
}
{
  error: 'Connection is closed.',
  stack: 'Error: Connection is closed.\n' +
    '    at EventEmitter.connectionCloseHandler (/Users/<USER>/Desktop/njback/node_modules/ioredis/built/Redis.js:208:28)\n' +
    '    at Object.onceWrapper (node:events:622:26)\n' +
    '    at EventEmitter.emit (node:events:519:35)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:85:11)',
  context: undefined,
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:17'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:17'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:17'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:17'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:17'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:17'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:17'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:18'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:18'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:18'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:18'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:18'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:18'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:19'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:19'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:19'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:19'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:19'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:19'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:20'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:20'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:20'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:20'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:21'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:21'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:22'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:22'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:22'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:22'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:23'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:23'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:24'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:24'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:25'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:25'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:26'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:26'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:27'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:27'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:28'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:28'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:29'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:29'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:30'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:30'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:31'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:31'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:32'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:32'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:33'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:33'
}
{
  error: 'Connection is closed.',
  stack: 'Error: Connection is closed.\n' +
    '    at EventEmitter.connectionCloseHandler (/Users/<USER>/Desktop/njback/node_modules/ioredis/built/Redis.js:208:28)\n' +
    '    at Object.onceWrapper (node:events:622:26)\n' +
    '    at EventEmitter.emit (node:events:519:35)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:85:11)',
  level: 'error',
  message: 'Failed to connect to Redis',
  timestamp: '2025-08-05 00:32:33'
}
{
  error: 'Connection is closed.',
  stack: 'Error: Connection is closed.\n' +
    '    at EventEmitter.connectionCloseHandler (/Users/<USER>/Desktop/njback/node_modules/ioredis/built/Redis.js:208:28)\n' +
    '    at Object.onceWrapper (node:events:622:26)\n' +
    '    at EventEmitter.emit (node:events:519:35)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:85:11)',
  context: undefined,
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:33'
}
{
  error: 'Connection is closed.',
  stack: 'Error: Connection is closed.\n' +
    '    at EventEmitter.connectionCloseHandler (/Users/<USER>/Desktop/njback/node_modules/ioredis/built/Redis.js:208:28)\n' +
    '    at Object.onceWrapper (node:events:622:26)\n' +
    '    at EventEmitter.emit (node:events:519:35)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:85:11)',
  level: 'error',
  message: 'Failed to connect Redis for Socket.IO service',
  timestamp: '2025-08-05 00:32:33'
}
{
  error: 'Connection is closed.',
  stack: 'Error: Connection is closed.\n' +
    '    at EventEmitter.connectionCloseHandler (/Users/<USER>/Desktop/njback/node_modules/ioredis/built/Redis.js:208:28)\n' +
    '    at Object.onceWrapper (node:events:622:26)\n' +
    '    at EventEmitter.emit (node:events:519:35)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:85:11)',
  context: undefined,
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:33'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:33'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:33'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:33'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:33'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:33'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:33'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:34'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:34'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:34'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:34'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:34'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:34'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:35'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:35'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:35'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:35'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:35'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:35'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:36'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:36'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:36'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:36'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:37'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:37'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:38'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:38'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:38'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:38'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:39'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:39'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:40'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:40'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:41'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:41'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:42'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:42'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:43'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:43'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:44'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:44'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:45'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:45'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:46'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:46'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:47'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:47'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:48'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:48'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:49'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:49'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:51'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:51'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:52'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:52'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:54'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:54'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:55'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:55'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:57'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:57'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:32:58'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:32:58'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:00'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:00'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:01'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:01'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:03'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:03'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:05'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:05'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:07'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:07'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:08'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:08'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:10'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:10'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:12'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:12'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:14'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:14'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:16'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:16'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:18'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:18'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:20'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:20'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:22'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:22'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:24'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:24'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:26'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:26'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:28'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:28'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:30'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:30'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:32'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:32'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:34'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:34'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:36'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:36'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:38'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:38'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:40'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:40'
}
{
  error: 'Connection is closed.',
  stack: 'Error: Connection is closed.\n' +
    '    at EventEmitter.connectionCloseHandler (/Users/<USER>/Desktop/njback/node_modules/ioredis/built/Redis.js:208:28)\n' +
    '    at Object.onceWrapper (node:events:622:26)\n' +
    '    at EventEmitter.emit (node:events:519:35)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:85:11)',
  level: 'error',
  message: 'Failed to connect to Redis',
  timestamp: '2025-08-05 00:33:40'
}
{
  error: 'Connection is closed.',
  stack: 'Error: Connection is closed.\n' +
    '    at EventEmitter.connectionCloseHandler (/Users/<USER>/Desktop/njback/node_modules/ioredis/built/Redis.js:208:28)\n' +
    '    at Object.onceWrapper (node:events:622:26)\n' +
    '    at EventEmitter.emit (node:events:519:35)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:85:11)',
  context: undefined,
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:40'
}
{
  error: 'Connection is closed.',
  stack: 'Error: Connection is closed.\n' +
    '    at EventEmitter.connectionCloseHandler (/Users/<USER>/Desktop/njback/node_modules/ioredis/built/Redis.js:208:28)\n' +
    '    at Object.onceWrapper (node:events:622:26)\n' +
    '    at EventEmitter.emit (node:events:519:35)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:85:11)',
  level: 'error',
  message: 'Failed to connect Redis for Socket.IO service',
  timestamp: '2025-08-05 00:33:40'
}
{
  error: 'Connection is closed.',
  stack: 'Error: Connection is closed.\n' +
    '    at EventEmitter.connectionCloseHandler (/Users/<USER>/Desktop/njback/node_modules/ioredis/built/Redis.js:208:28)\n' +
    '    at Object.onceWrapper (node:events:622:26)\n' +
    '    at EventEmitter.emit (node:events:519:35)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:85:11)',
  context: undefined,
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:40'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:40'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:40'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:40'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:40'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:40'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:40'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:40'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:40'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:40'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:40'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:41'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:41'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:41'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:41'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:41'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:41'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:42'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:42'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:42'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:42'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:43'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:43'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:44'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:44'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:44'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:44'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:45'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:45'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:46'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:46'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:46'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:46'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:47'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:47'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:48'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:48'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:49'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:49'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:50'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:50'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:51'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:51'
}
{
  error: 'Connection is closed.',
  stack: 'Error: Connection is closed.\n' +
    '    at EventEmitter.connectionCloseHandler (/Users/<USER>/Desktop/njback/node_modules/ioredis/built/Redis.js:208:28)\n' +
    '    at Object.onceWrapper (node:events:622:26)\n' +
    '    at EventEmitter.emit (node:events:519:35)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:85:11)',
  level: 'error',
  message: 'Failed to connect to Redis',
  timestamp: '2025-08-05 00:33:51'
}
{
  error: 'Connection is closed.',
  stack: 'Error: Connection is closed.\n' +
    '    at EventEmitter.connectionCloseHandler (/Users/<USER>/Desktop/njback/node_modules/ioredis/built/Redis.js:208:28)\n' +
    '    at Object.onceWrapper (node:events:622:26)\n' +
    '    at EventEmitter.emit (node:events:519:35)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:85:11)',
  context: undefined,
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:51'
}
{
  error: 'Connection is closed.',
  stack: 'Error: Connection is closed.\n' +
    '    at EventEmitter.connectionCloseHandler (/Users/<USER>/Desktop/njback/node_modules/ioredis/built/Redis.js:208:28)\n' +
    '    at Object.onceWrapper (node:events:622:26)\n' +
    '    at EventEmitter.emit (node:events:519:35)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:85:11)',
  level: 'error',
  message: 'Failed to connect Redis for Socket.IO service',
  timestamp: '2025-08-05 00:33:51'
}
{
  error: 'Connection is closed.',
  stack: 'Error: Connection is closed.\n' +
    '    at EventEmitter.connectionCloseHandler (/Users/<USER>/Desktop/njback/node_modules/ioredis/built/Redis.js:208:28)\n' +
    '    at Object.onceWrapper (node:events:622:26)\n' +
    '    at EventEmitter.emit (node:events:519:35)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:85:11)',
  context: undefined,
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:51'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:51'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:51'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:51'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:51'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:52'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:52'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:52'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:52'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:52'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:52'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:52'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:52'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:53'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:53'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:53'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:53'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:54'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:54'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:54'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:54'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:55'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:55'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:55'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:55'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:56'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:56'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:57'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:57'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:57'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:57'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:58'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:58'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:33:59'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:33:59'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:00'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:00'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:01'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:01'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:02'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:02'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:03'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:03'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:04'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:04'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:05'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:05'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:06'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:06'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:07'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:07'
}
{
  error: 'Connection is closed.',
  stack: 'Error: Connection is closed.\n' +
    '    at EventEmitter.connectionCloseHandler (/Users/<USER>/Desktop/njback/node_modules/ioredis/built/Redis.js:208:28)\n' +
    '    at Object.onceWrapper (node:events:622:26)\n' +
    '    at EventEmitter.emit (node:events:519:35)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:85:11)',
  level: 'error',
  message: 'Failed to connect to Redis',
  timestamp: '2025-08-05 00:34:07'
}
{
  error: 'Connection is closed.',
  stack: 'Error: Connection is closed.\n' +
    '    at EventEmitter.connectionCloseHandler (/Users/<USER>/Desktop/njback/node_modules/ioredis/built/Redis.js:208:28)\n' +
    '    at Object.onceWrapper (node:events:622:26)\n' +
    '    at EventEmitter.emit (node:events:519:35)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:85:11)',
  context: undefined,
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:07'
}
{
  error: 'Connection is closed.',
  stack: 'Error: Connection is closed.\n' +
    '    at EventEmitter.connectionCloseHandler (/Users/<USER>/Desktop/njback/node_modules/ioredis/built/Redis.js:208:28)\n' +
    '    at Object.onceWrapper (node:events:622:26)\n' +
    '    at EventEmitter.emit (node:events:519:35)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:85:11)',
  level: 'error',
  message: 'Failed to connect Redis for Socket.IO service',
  timestamp: '2025-08-05 00:34:07'
}
{
  error: 'Connection is closed.',
  stack: 'Error: Connection is closed.\n' +
    '    at EventEmitter.connectionCloseHandler (/Users/<USER>/Desktop/njback/node_modules/ioredis/built/Redis.js:208:28)\n' +
    '    at Object.onceWrapper (node:events:622:26)\n' +
    '    at EventEmitter.emit (node:events:519:35)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:85:11)',
  context: undefined,
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:07'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:07'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:07'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:07'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:07'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:07'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:07'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:08'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:08'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:08'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:08'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:08'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:08'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:08'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:08'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:09'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:09'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:09'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:09'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:10'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:10'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:10'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:10'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:11'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:11'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:12'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:12'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:12'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:12'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:13'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:13'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:14'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:14'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:15'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:15'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:16'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:16'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:17'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:17'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:18'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:18'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:19'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:19'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:20'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:20'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:21'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:21'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:22'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:22'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:23'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:23'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:25'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:25'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:26'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:26'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:27'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:27'
}
{
  error: 'Connection is closed.',
  stack: 'Error: Connection is closed.\n' +
    '    at EventEmitter.connectionCloseHandler (/Users/<USER>/Desktop/njback/node_modules/ioredis/built/Redis.js:208:28)\n' +
    '    at Object.onceWrapper (node:events:622:26)\n' +
    '    at EventEmitter.emit (node:events:519:35)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:85:11)',
  level: 'error',
  message: 'Failed to connect to Redis',
  timestamp: '2025-08-05 00:34:27'
}
{
  error: 'Connection is closed.',
  stack: 'Error: Connection is closed.\n' +
    '    at EventEmitter.connectionCloseHandler (/Users/<USER>/Desktop/njback/node_modules/ioredis/built/Redis.js:208:28)\n' +
    '    at Object.onceWrapper (node:events:622:26)\n' +
    '    at EventEmitter.emit (node:events:519:35)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:85:11)',
  context: undefined,
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:27'
}
{
  error: 'Connection is closed.',
  stack: 'Error: Connection is closed.\n' +
    '    at EventEmitter.connectionCloseHandler (/Users/<USER>/Desktop/njback/node_modules/ioredis/built/Redis.js:208:28)\n' +
    '    at Object.onceWrapper (node:events:622:26)\n' +
    '    at EventEmitter.emit (node:events:519:35)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:85:11)',
  level: 'error',
  message: 'Failed to connect Redis for Socket.IO service',
  timestamp: '2025-08-05 00:34:27'
}
{
  error: 'Connection is closed.',
  stack: 'Error: Connection is closed.\n' +
    '    at EventEmitter.connectionCloseHandler (/Users/<USER>/Desktop/njback/node_modules/ioredis/built/Redis.js:208:28)\n' +
    '    at Object.onceWrapper (node:events:622:26)\n' +
    '    at EventEmitter.emit (node:events:519:35)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:85:11)',
  context: undefined,
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:27'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:27'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:27'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:27'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:27'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:27'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:27'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:27'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:27'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:28'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:28'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:28'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:28'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:28'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:28'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:29'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:29'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:29'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:29'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:30'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:30'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:30'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:30'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:31'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:31'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:31'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:31'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:32'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:32'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:33'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:33'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:34'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:34'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:34'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:34'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:35'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:35'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:36'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:36'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:37'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:37'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:38'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:38'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:40'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:40'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:41'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:41'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:42'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:42'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:43'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:43'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:44'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:44'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:46'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:46'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:47'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:47'
}
{
  error: 'Connection is closed.',
  stack: 'Error: Connection is closed.\n' +
    '    at EventEmitter.connectionCloseHandler (/Users/<USER>/Desktop/njback/node_modules/ioredis/built/Redis.js:208:28)\n' +
    '    at Object.onceWrapper (node:events:622:26)\n' +
    '    at EventEmitter.emit (node:events:519:35)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:85:11)',
  level: 'error',
  message: 'Failed to connect to Redis',
  timestamp: '2025-08-05 00:34:47'
}
{
  error: 'Connection is closed.',
  stack: 'Error: Connection is closed.\n' +
    '    at EventEmitter.connectionCloseHandler (/Users/<USER>/Desktop/njback/node_modules/ioredis/built/Redis.js:208:28)\n' +
    '    at Object.onceWrapper (node:events:622:26)\n' +
    '    at EventEmitter.emit (node:events:519:35)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:85:11)',
  context: undefined,
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:47'
}
{
  error: 'Connection is closed.',
  stack: 'Error: Connection is closed.\n' +
    '    at EventEmitter.connectionCloseHandler (/Users/<USER>/Desktop/njback/node_modules/ioredis/built/Redis.js:208:28)\n' +
    '    at Object.onceWrapper (node:events:622:26)\n' +
    '    at EventEmitter.emit (node:events:519:35)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:85:11)',
  level: 'error',
  message: 'Failed to connect Redis for Socket.IO service',
  timestamp: '2025-08-05 00:34:47'
}
{
  error: 'Connection is closed.',
  stack: 'Error: Connection is closed.\n' +
    '    at EventEmitter.connectionCloseHandler (/Users/<USER>/Desktop/njback/node_modules/ioredis/built/Redis.js:208:28)\n' +
    '    at Object.onceWrapper (node:events:622:26)\n' +
    '    at EventEmitter.emit (node:events:519:35)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:85:11)',
  context: undefined,
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:47'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:47'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:47'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:47'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:47'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:48'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:48'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:48'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:48'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:48'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:48'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:48'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:48'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:49'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:49'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:49'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:49'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:50'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:50'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:50'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:50'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:51'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:51'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:51'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:51'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:52'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:52'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:53'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:53'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:53'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:53'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:54'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:54'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:55'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:55'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:56'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:56'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:57'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:57'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:58'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:58'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:34:59'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:34:59'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:35:00'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:35:00'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:35:01'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:35:01'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:35:02'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:35:02'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:35:04'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:35:04'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:35:05'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:35:05'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:35:06'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:35:06'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:35:08'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:35:08'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:35:09'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:35:09'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:35:11'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:35:11'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:35:12'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:35:12'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:35:14'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:35:14'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:35:15'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:35:15'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:35:17'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:35:17'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:35:19'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:35:19'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:35:21'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:35:21'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:35:23'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:35:23'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:35:24'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:35:24'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:35:26'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:35:26'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:35:28'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:35:28'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:35:30'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:35:30'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:35:32'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:35:32'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:35:34'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:35:34'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:35:36'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:35:36'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:35:38'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:35:38'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:35:40'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:35:40'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:35:42'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:35:42'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:35:44'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:35:44'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:35:46'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:35:46'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:35:48'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:35:48'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:35:50'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:35:50'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:35:52'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:35:52'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:35:54'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:35:54'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:35:56'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:35:56'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:35:59'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:35:59'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:36:01'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:36:01'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:36:03'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:36:03'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:36:05'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:36:05'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:36:07'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:36:07'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:36:09'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:36:09'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:36:11'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:36:11'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:36:13'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:36:13'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:36:15'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:36:15'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:36:17'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:36:17'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:36:19'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:36:19'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:36:21'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:36:21'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:36:23'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:36:23'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:36:25'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:36:25'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:36:27'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:36:27'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:36:29'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:36:29'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:36:31'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:36:31'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:36:33'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:36:33'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:36:35'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:36:35'
}
{
  host: 'localhost',
  port: 6379,
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  level: 'error',
  message: 'Redis connection error',
  timestamp: '2025-08-05 00:36:37'
}
{
  error: 'connect ECONNREFUSED 127.0.0.1:6379',
  stack: 'Error: connect ECONNREFUSED 127.0.0.1:6379\n' +
    '    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)',
  context: { host: 'localhost', port: 6379 },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:36:37'
}
{
  error: 'listen EADDRINUSE: address already in use :::3000',
  stack: 'Error: listen EADDRINUSE: address already in use :::3000\n' +
    '    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n' +
    '    at listenInCluster (node:net:1996:12)\n' +
    '    at Server.listen (node:net:2101:7)\n' +
    '    at startServer (/Users/<USER>/Desktop/njback/dist/server.js:32:35)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)',
  level: 'error',
  message: 'Uncaught Exception',
  timestamp: '2025-08-05 00:38:24'
}
{
  requestId: '1b62b887-c5d2-458f-a7ed-d6fa5c59b527',
  method: 'POST',
  url: '/api/auth/register',
  ip: '::1',
  userAgent: 'axios/1.11.0',
  userId: undefined,
  responseTime: 8,
  body: {
    username: 'sockettest',
    email: '<EMAIL>',
    password: '[REDACTED]'
  },
  query: {},
  params: {},
  error: 'Validation failed',
  stack: 'Error: Validation failed\n' +
    '    at /Users/<USER>/Desktop/njback/dist/api/middlewares/validation.middleware.js:18:23\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)',
  level: 'error',
  message: 'Request failed: POST /api/auth/register',
  timestamp: '2025-08-05 00:39:34'
}
{
  error: 'Validation failed',
  stack: 'Error: Validation failed\n' +
    '    at /Users/<USER>/Desktop/njback/dist/api/middlewares/validation.middleware.js:18:23\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)',
  context: {
    requestId: '1b62b887-c5d2-458f-a7ed-d6fa5c59b527',
    method: 'POST',
    url: '/api/auth/register',
    ip: '::1',
    userAgent: 'axios/1.11.0',
    userId: undefined,
    responseTime: 8,
    body: {
      username: 'sockettest',
      email: '<EMAIL>',
      password: '[REDACTED]'
    },
    query: {},
    params: {}
  },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:39:34'
}
{
  method: 'POST',
  url: '/api/auth/register',
  ip: '::1',
  userAgent: 'axios/1.11.0',
  statusCode: 400,
  responseTime: 9,
  userId: undefined,
  category: 'api',
  level: 'error',
  message: '[API] POST /api/auth/register - 400 - 9ms',
  timestamp: '2025-08-05 00:39:34'
}
{
  error: 'listen EADDRINUSE: address already in use :::3002',
  stack: 'Error: listen EADDRINUSE: address already in use :::3002\n' +
    '    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n' +
    '    at listenInCluster (node:net:1996:12)\n' +
    '    at Server.listen (node:net:2101:7)\n' +
    '    at startServer (/Users/<USER>/Desktop/njback/src/server.ts:34:31)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:105:5)',
  level: 'error',
  message: 'Uncaught Exception',
  timestamp: '2025-08-05 00:41:06'
}
{
  requestId: 'c4709a8f-3caa-4c36-a373-16a37778c7c1',
  method: 'POST',
  url: '/api/auth/register',
  ip: '::1',
  userAgent: 'axios/1.11.0',
  userId: undefined,
  responseTime: 15,
  body: {
    username: 'sockettest',
    email: '<EMAIL>',
    password: '[REDACTED]'
  },
  query: {},
  params: {},
  error: 'User with this email already exists',
  stack: 'Error: User with this email already exists\n' +
    '    at AuthService.register (/Users/<USER>/Desktop/njback/dist/services/auth.service.js:25:23)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async AuthController.register (/Users/<USER>/Desktop/njback/dist/api/controllers/auth.controller.js:14:32)',
  level: 'error',
  message: 'Request failed: POST /api/auth/register',
  timestamp: '2025-08-05 00:41:50'
}
{
  error: 'User with this email already exists',
  stack: 'Error: User with this email already exists\n' +
    '    at AuthService.register (/Users/<USER>/Desktop/njback/dist/services/auth.service.js:25:23)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async AuthController.register (/Users/<USER>/Desktop/njback/dist/api/controllers/auth.controller.js:14:32)',
  context: {
    requestId: 'c4709a8f-3caa-4c36-a373-16a37778c7c1',
    method: 'POST',
    url: '/api/auth/register',
    ip: '::1',
    userAgent: 'axios/1.11.0',
    userId: undefined,
    responseTime: 15,
    body: {
      username: 'sockettest',
      email: '<EMAIL>',
      password: '[REDACTED]'
    },
    query: {},
    params: {}
  },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:41:50'
}
{
  method: 'POST',
  url: '/api/auth/register',
  ip: '::1',
  userAgent: 'axios/1.11.0',
  statusCode: 400,
  responseTime: 16,
  userId: undefined,
  category: 'api',
  level: 'error',
  message: '[API] POST /api/auth/register - 400 - 16ms',
  timestamp: '2025-08-05 00:41:50'
}
{
  error: 'listen EADDRINUSE: address already in use :::3002',
  stack: 'Error: listen EADDRINUSE: address already in use :::3002\n' +
    '    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n' +
    '    at listenInCluster (node:net:1996:12)\n' +
    '    at Server.listen (node:net:2101:7)\n' +
    '    at startServer (/Users/<USER>/Desktop/njback/src/server.ts:34:31)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:105:5)',
  level: 'error',
  message: 'Uncaught Exception',
  timestamp: '2025-08-05 00:42:39'
}
{
  socketId: 'NLzpXFwQUNmvsZPlAAAD',
  ip: '::1',
  userAgent: 'node-XMLHttpRequest',
  error: 'jwt malformed',
  stack: 'JsonWebTokenError: jwt malformed\n' +
    '    at module.exports [as verify] (/Users/<USER>/Desktop/njback/node_modules/jsonwebtoken/verify.js:70:17)\n' +
    '    at Array.socketAuthMiddleware (/Users/<USER>/Desktop/njback/dist/api/middlewares/socket.middleware.js:39:48)\n' +
    '    at run (/Users/<USER>/Desktop/njback/node_modules/socket.io/dist/namespace.js:130:19)\n' +
    '    at Namespace.run (/Users/<USER>/Desktop/njback/node_modules/socket.io/dist/namespace.js:141:9)\n' +
    '    at Namespace._add (/Users/<USER>/Desktop/njback/node_modules/socket.io/dist/namespace.js:218:14)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)',
  level: 'error',
  message: 'Socket authentication failed',
  timestamp: '2025-08-05 00:43:54'
}
{
  error: 'jwt malformed',
  stack: 'JsonWebTokenError: jwt malformed\n' +
    '    at module.exports [as verify] (/Users/<USER>/Desktop/njback/node_modules/jsonwebtoken/verify.js:70:17)\n' +
    '    at Array.socketAuthMiddleware (/Users/<USER>/Desktop/njback/dist/api/middlewares/socket.middleware.js:39:48)\n' +
    '    at run (/Users/<USER>/Desktop/njback/node_modules/socket.io/dist/namespace.js:130:19)\n' +
    '    at Namespace.run (/Users/<USER>/Desktop/njback/node_modules/socket.io/dist/namespace.js:141:9)\n' +
    '    at Namespace._add (/Users/<USER>/Desktop/njback/node_modules/socket.io/dist/namespace.js:218:14)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)',
  context: {
    socketId: 'NLzpXFwQUNmvsZPlAAAD',
    ip: '::1',
    userAgent: 'node-XMLHttpRequest'
  },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:43:54'
}
{
  socketId: 'fTF4-5z-giDXFWu1AAAF',
  ip: '::1',
  userAgent: 'node-XMLHttpRequest',
  error: 'jwt malformed',
  stack: 'JsonWebTokenError: jwt malformed\n' +
    '    at module.exports [as verify] (/Users/<USER>/Desktop/njback/node_modules/jsonwebtoken/verify.js:70:17)\n' +
    '    at Array.socketAuthMiddleware (/Users/<USER>/Desktop/njback/dist/api/middlewares/socket.middleware.js:39:48)\n' +
    '    at run (/Users/<USER>/Desktop/njback/node_modules/socket.io/dist/namespace.js:130:19)\n' +
    '    at Namespace.run (/Users/<USER>/Desktop/njback/node_modules/socket.io/dist/namespace.js:141:9)\n' +
    '    at Namespace._add (/Users/<USER>/Desktop/njback/node_modules/socket.io/dist/namespace.js:218:14)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)',
  level: 'error',
  message: 'Socket authentication failed',
  timestamp: '2025-08-05 00:44:32'
}
{
  error: 'jwt malformed',
  stack: 'JsonWebTokenError: jwt malformed\n' +
    '    at module.exports [as verify] (/Users/<USER>/Desktop/njback/node_modules/jsonwebtoken/verify.js:70:17)\n' +
    '    at Array.socketAuthMiddleware (/Users/<USER>/Desktop/njback/dist/api/middlewares/socket.middleware.js:39:48)\n' +
    '    at run (/Users/<USER>/Desktop/njback/node_modules/socket.io/dist/namespace.js:130:19)\n' +
    '    at Namespace.run (/Users/<USER>/Desktop/njback/node_modules/socket.io/dist/namespace.js:141:9)\n' +
    '    at Namespace._add (/Users/<USER>/Desktop/njback/node_modules/socket.io/dist/namespace.js:218:14)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)',
  context: {
    socketId: 'fTF4-5z-giDXFWu1AAAF',
    ip: '::1',
    userAgent: 'node-XMLHttpRequest'
  },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 00:44:32'
}
{
  error: 'listen EADDRINUSE: address already in use :::3002',
  stack: 'Error: listen EADDRINUSE: address already in use :::3002\n' +
    '    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n' +
    '    at listenInCluster (node:net:1996:12)\n' +
    '    at Server.listen (node:net:2101:7)\n' +
    '    at startServer (/Users/<USER>/Desktop/njback/src/server.ts:34:31)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:105:5)',
  level: 'error',
  message: 'Uncaught Exception',
  timestamp: '2025-08-05 00:45:54'
}
{
  error: 'listen EADDRINUSE: address already in use :::3002',
  stack: 'Error: listen EADDRINUSE: address already in use :::3002\n' +
    '    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n' +
    '    at listenInCluster (node:net:1996:12)\n' +
    '    at Server.listen (node:net:2101:7)\n' +
    '    at startServer (/Users/<USER>/Desktop/njback/src/server.ts:34:31)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:105:5)',
  level: 'error',
  message: 'Uncaught Exception',
  timestamp: '2025-08-05 00:47:43'
}
{
  error: 'listen EADDRINUSE: address already in use :::3002',
  stack: 'Error: listen EADDRINUSE: address already in use :::3002\n' +
    '    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n' +
    '    at listenInCluster (node:net:1996:12)\n' +
    '    at Server.listen (node:net:2101:7)\n' +
    '    at startServer (/Users/<USER>/Desktop/njback/src/server.ts:34:31)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:105:5)',
  level: 'error',
  message: 'Uncaught Exception',
  timestamp: '2025-08-05 00:47:45'
}
{
  error: 'listen EADDRINUSE: address already in use :::3002',
  stack: 'Error: listen EADDRINUSE: address already in use :::3002\n' +
    '    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n' +
    '    at listenInCluster (node:net:1996:12)\n' +
    '    at Server.listen (node:net:2101:7)\n' +
    '    at startServer (/Users/<USER>/Desktop/njback/src/server.ts:34:31)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:105:5)',
  level: 'error',
  message: 'Uncaught Exception',
  timestamp: '2025-08-05 00:47:49'
}
{
  error: 'listen EADDRINUSE: address already in use :::3002',
  stack: 'Error: listen EADDRINUSE: address already in use :::3002\n' +
    '    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n' +
    '    at listenInCluster (node:net:1996:12)\n' +
    '    at Server.listen (node:net:2101:7)\n' +
    '    at startServer (/Users/<USER>/Desktop/njback/src/server.ts:34:31)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:105:5)',
  level: 'error',
  message: 'Uncaught Exception',
  timestamp: '2025-08-05 00:47:53'
}
{
  error: 'listen EADDRINUSE: address already in use :::3002',
  stack: 'Error: listen EADDRINUSE: address already in use :::3002\n' +
    '    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n' +
    '    at listenInCluster (node:net:1996:12)\n' +
    '    at Server.listen (node:net:2101:7)\n' +
    '    at startServer (/Users/<USER>/Desktop/njback/src/server.ts:34:31)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:105:5)',
  level: 'error',
  message: 'Uncaught Exception',
  timestamp: '2025-08-05 00:47:55'
}
{
  error: 'listen EADDRINUSE: address already in use :::3002',
  stack: 'Error: listen EADDRINUSE: address already in use :::3002\n' +
    '    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n' +
    '    at listenInCluster (node:net:1996:12)\n' +
    '    at Server.listen (node:net:2101:7)\n' +
    '    at startServer (/Users/<USER>/Desktop/njback/src/server.ts:34:31)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:105:5)',
  level: 'error',
  message: 'Uncaught Exception',
  timestamp: '2025-08-05 00:48:21'
}
{
  error: 'listen EADDRINUSE: address already in use :::3002',
  stack: 'Error: listen EADDRINUSE: address already in use :::3002\n' +
    '    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n' +
    '    at listenInCluster (node:net:1996:12)\n' +
    '    at Server.listen (node:net:2101:7)\n' +
    '    at startServer (/Users/<USER>/Desktop/njback/src/server.ts:34:31)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:105:5)',
  level: 'error',
  message: 'Uncaught Exception',
  timestamp: '2025-08-05 00:48:24'
}
{
  error: 'listen EADDRINUSE: address already in use :::3002',
  stack: 'Error: listen EADDRINUSE: address already in use :::3002\n' +
    '    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n' +
    '    at listenInCluster (node:net:1996:12)\n' +
    '    at Server.listen (node:net:2101:7)\n' +
    '    at startServer (/Users/<USER>/Desktop/njback/src/server.ts:34:31)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:105:5)',
  level: 'error',
  message: 'Uncaught Exception',
  timestamp: '2025-08-05 00:48:25'
}
{
  error: 'listen EADDRINUSE: address already in use :::3002',
  stack: 'Error: listen EADDRINUSE: address already in use :::3002\n' +
    '    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n' +
    '    at listenInCluster (node:net:1996:12)\n' +
    '    at Server.listen (node:net:2101:7)\n' +
    '    at startServer (/Users/<USER>/Desktop/njback/src/server.ts:34:31)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:105:5)',
  level: 'error',
  message: 'Uncaught Exception',
  timestamp: '2025-08-05 00:48:27'
}
{
  error: 'listen EADDRINUSE: address already in use :::3002',
  stack: 'Error: listen EADDRINUSE: address already in use :::3002\n' +
    '    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n' +
    '    at listenInCluster (node:net:1996:12)\n' +
    '    at Server.listen (node:net:2101:7)\n' +
    '    at startServer (/Users/<USER>/Desktop/njback/src/server.ts:34:31)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:105:5)',
  level: 'error',
  message: 'Uncaught Exception',
  timestamp: '2025-08-05 00:48:31'
}
{
  error: 'listen EADDRINUSE: address already in use :::3002',
  stack: 'Error: listen EADDRINUSE: address already in use :::3002\n' +
    '    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n' +
    '    at listenInCluster (node:net:1996:12)\n' +
    '    at Server.listen (node:net:2101:7)\n' +
    '    at startServer (/Users/<USER>/Desktop/njback/src/server.ts:34:31)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:105:5)',
  level: 'error',
  message: 'Uncaught Exception',
  timestamp: '2025-08-05 00:48:32'
}
{
  conversationId: '68912ddaef87f5d380f7aa57',
  userId: '68912dd9ef87f5d380f7aa4d',
  error: 'Access denied: You are not a participant in this conversation',
  stack: 'Error: Access denied: You are not a participant in this conversation\n' +
    '    at ConversationService.getConversationById (/Users/<USER>/Desktop/njback/dist/services/conversation.service.js:114:23)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async getConversation (/Users/<USER>/Desktop/njback/dist/api/controllers/conversation.controller.js:114:34)',
  level: 'error',
  message: 'Failed to get conversation by ID',
  timestamp: '2025-08-05 01:02:02'
}
{
  currentUserId: '68912e27d306d81dcbf04f22',
  participantId: '507f1f77bcf86cd799439011',
  error: 'Participant user not found',
  stack: 'Error: Participant user not found\n' +
    '    at ConversationService.createOrFindConversation (/Users/<USER>/Desktop/njback/dist/services/conversation.service.js:26:23)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async createConversation (/Users/<USER>/Desktop/njback/dist/api/controllers/conversation.controller.js:19:34)',
  level: 'error',
  message: 'Failed to create or find conversation',
  timestamp: '2025-08-05 01:03:20'
}
{
  requestId: '022791a3-cd1b-4886-9670-c1963a4e317b',
  method: 'POST',
  url: '/api/auth/login',
  ip: '::1',
  userAgent: 'node',
  userId: undefined,
  responseTime: 216,
  body: { email: '<EMAIL>', password: '[REDACTED]' },
  query: {},
  params: {},
  error: 'Invalid email or password',
  stack: 'Error: Invalid email or password\n' +
    '    at AuthService.login (/Users/<USER>/Desktop/njback/src/services/auth.service.ts:158:15)\n' +
    '    at AuthController.login (/Users/<USER>/Desktop/njback/src/api/controllers/auth.controller.ts:33:22)',
  level: 'error',
  message: 'Request failed: POST /api/auth/login',
  timestamp: '2025-08-05 09:02:38'
}
{
  error: 'Invalid email or password',
  stack: 'Error: Invalid email or password\n' +
    '    at AuthService.login (/Users/<USER>/Desktop/njback/src/services/auth.service.ts:158:15)\n' +
    '    at AuthController.login (/Users/<USER>/Desktop/njback/src/api/controllers/auth.controller.ts:33:22)',
  context: {
    requestId: '022791a3-cd1b-4886-9670-c1963a4e317b',
    method: 'POST',
    url: '/api/auth/login',
    ip: '::1',
    userAgent: 'node',
    userId: undefined,
    responseTime: 216,
    body: { email: '<EMAIL>', password: '[REDACTED]' },
    query: {},
    params: {}
  },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 09:02:38'
}
{
  method: 'POST',
  url: '/api/auth/login',
  ip: '::1',
  userAgent: 'node',
  statusCode: 401,
  responseTime: 219,
  userId: undefined,
  category: 'api',
  level: 'error',
  message: '[API] POST /api/auth/login - 401 - 219ms',
  timestamp: '2025-08-05 09:02:38'
}
{
  requestId: '651b89e6-9b9f-4533-b855-aae28dee2e7a',
  method: 'POST',
  url: '/api/auth/register',
  ip: '::1',
  userAgent: 'node',
  userId: undefined,
  responseTime: 2,
  body: {
    username: 'testuser',
    email: '<EMAIL>',
    password: '[REDACTED]'
  },
  query: {},
  params: {},
  error: 'Validation failed',
  stack: 'Error: Validation failed\n' +
    '    at /Users/<USER>/Desktop/njback/src/api/middlewares/validation.middleware.ts:24:15\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:105:5)',
  level: 'error',
  message: 'Request failed: POST /api/auth/register',
  timestamp: '2025-08-05 09:02:38'
}
{
  error: 'Validation failed',
  stack: 'Error: Validation failed\n' +
    '    at /Users/<USER>/Desktop/njback/src/api/middlewares/validation.middleware.ts:24:15\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:105:5)',
  context: {
    requestId: '651b89e6-9b9f-4533-b855-aae28dee2e7a',
    method: 'POST',
    url: '/api/auth/register',
    ip: '::1',
    userAgent: 'node',
    userId: undefined,
    responseTime: 2,
    body: {
      username: 'testuser',
      email: '<EMAIL>',
      password: '[REDACTED]'
    },
    query: {},
    params: {}
  },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 09:02:38'
}
{
  method: 'POST',
  url: '/api/auth/register',
  ip: '::1',
  userAgent: 'node',
  statusCode: 400,
  responseTime: 2,
  userId: undefined,
  category: 'api',
  level: 'error',
  message: '[API] POST /api/auth/register - 400 - 2ms',
  timestamp: '2025-08-05 09:02:38'
}
{
  requestId: '7068f7a4-96c9-4d63-a5ce-d49dbd3fb66d',
  method: 'POST',
  url: '/api/auth/login',
  ip: '::1',
  userAgent: 'node',
  userId: undefined,
  responseTime: 207,
  body: { email: '<EMAIL>', password: '[REDACTED]' },
  query: {},
  params: {},
  error: 'Invalid email or password',
  stack: 'Error: Invalid email or password\n' +
    '    at AuthService.login (/Users/<USER>/Desktop/njback/src/services/auth.service.ts:158:15)\n' +
    '    at AuthController.login (/Users/<USER>/Desktop/njback/src/api/controllers/auth.controller.ts:33:22)',
  level: 'error',
  message: 'Request failed: POST /api/auth/login',
  timestamp: '2025-08-05 09:03:55'
}
{
  error: 'Invalid email or password',
  stack: 'Error: Invalid email or password\n' +
    '    at AuthService.login (/Users/<USER>/Desktop/njback/src/services/auth.service.ts:158:15)\n' +
    '    at AuthController.login (/Users/<USER>/Desktop/njback/src/api/controllers/auth.controller.ts:33:22)',
  context: {
    requestId: '7068f7a4-96c9-4d63-a5ce-d49dbd3fb66d',
    method: 'POST',
    url: '/api/auth/login',
    ip: '::1',
    userAgent: 'node',
    userId: undefined,
    responseTime: 207,
    body: { email: '<EMAIL>', password: '[REDACTED]' },
    query: {},
    params: {}
  },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 09:03:55'
}
{
  method: 'POST',
  url: '/api/auth/login',
  ip: '::1',
  userAgent: 'node',
  statusCode: 401,
  responseTime: 208,
  userId: undefined,
  category: 'api',
  level: 'error',
  message: '[API] POST /api/auth/login - 401 - 208ms',
  timestamp: '2025-08-05 09:03:55'
}
{
  requestId: '3195f1ef-2450-40a5-b310-61c35de1e3ee',
  method: 'POST',
  url: '/api/auth/register',
  ip: '::1',
  userAgent: 'node',
  userId: undefined,
  responseTime: 1,
  body: {
    username: 'testuser',
    email: '<EMAIL>',
    password: '[REDACTED]'
  },
  query: {},
  params: {},
  error: 'Validation failed',
  stack: 'Error: Validation failed\n' +
    '    at /Users/<USER>/Desktop/njback/src/api/middlewares/validation.middleware.ts:24:15\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:105:5)',
  level: 'error',
  message: 'Request failed: POST /api/auth/register',
  timestamp: '2025-08-05 09:03:55'
}
{
  error: 'Validation failed',
  stack: 'Error: Validation failed\n' +
    '    at /Users/<USER>/Desktop/njback/src/api/middlewares/validation.middleware.ts:24:15\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:105:5)',
  context: {
    requestId: '3195f1ef-2450-40a5-b310-61c35de1e3ee',
    method: 'POST',
    url: '/api/auth/register',
    ip: '::1',
    userAgent: 'node',
    userId: undefined,
    responseTime: 1,
    body: {
      username: 'testuser',
      email: '<EMAIL>',
      password: '[REDACTED]'
    },
    query: {},
    params: {}
  },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 09:03:55'
}
{
  method: 'POST',
  url: '/api/auth/register',
  ip: '::1',
  userAgent: 'node',
  statusCode: 400,
  responseTime: 3,
  userId: undefined,
  category: 'api',
  level: 'error',
  message: '[API] POST /api/auth/register - 400 - 3ms',
  timestamp: '2025-08-05 09:03:55'
}
{
  socketId: 'R_7_REze4FOwOTqCAAAH',
  userId: '6891106a6280acb97804e1d6',
  conversationId: 'invalid-conversation-id',
  category: 'socket',
  error: 'Cast to ObjectId failed for value "invalid-conversation-id" (type string) at path "_id" for model "Conversation"',
  stack: 'CastError: Cast to ObjectId failed for value "invalid-conversation-id" (type string) at path "_id" for model "Conversation"\n' +
    '    at SchemaObjectId.cast (/Users/<USER>/Desktop/njback/node_modules/mongoose/lib/schema/objectId.js:251:11)\n' +
    '    at SchemaObjectId.SchemaType.applySetters (/Users/<USER>/Desktop/njback/node_modules/mongoose/lib/schemaType.js:1258:12)\n' +
    '    at SchemaObjectId.SchemaType.castForQuery (/Users/<USER>/Desktop/njback/node_modules/mongoose/lib/schemaType.js:1680:17)\n' +
    '    at cast (/Users/<USER>/Desktop/njback/node_modules/mongoose/lib/cast.js:390:32)\n' +
    '    at model.Query.Query.cast (/Users/<USER>/Desktop/njback/node_modules/mongoose/lib/query.js:5055:12)\n' +
    '    at model.Query.Query._castConditions (/Users/<USER>/Desktop/njback/node_modules/mongoose/lib/query.js:2351:10)\n' +
    '    at model.Query._findOne (/Users/<USER>/Desktop/njback/node_modules/mongoose/lib/query.js:2674:8)\n' +
    '    at model.Query.exec (/Users/<USER>/Desktop/njback/node_modules/mongoose/lib/query.js:4604:80)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at SocketController.handleJoinConversation (/Users/<USER>/Desktop/njback/src/socket/socket.controller.ts:58:28)',
  level: 'error',
  message: '[SOCKET] Error joining conversation',
  timestamp: '2025-08-05 09:06:53'
}
{
  error: 'Cast to ObjectId failed for value "invalid-conversation-id" (type string) at path "_id" for model "Conversation"',
  stack: 'CastError: Cast to ObjectId failed for value "invalid-conversation-id" (type string) at path "_id" for model "Conversation"\n' +
    '    at SchemaObjectId.cast (/Users/<USER>/Desktop/njback/node_modules/mongoose/lib/schema/objectId.js:251:11)\n' +
    '    at SchemaObjectId.SchemaType.applySetters (/Users/<USER>/Desktop/njback/node_modules/mongoose/lib/schemaType.js:1258:12)\n' +
    '    at SchemaObjectId.SchemaType.castForQuery (/Users/<USER>/Desktop/njback/node_modules/mongoose/lib/schemaType.js:1680:17)\n' +
    '    at cast (/Users/<USER>/Desktop/njback/node_modules/mongoose/lib/cast.js:390:32)\n' +
    '    at model.Query.Query.cast (/Users/<USER>/Desktop/njback/node_modules/mongoose/lib/query.js:5055:12)\n' +
    '    at model.Query.Query._castConditions (/Users/<USER>/Desktop/njback/node_modules/mongoose/lib/query.js:2351:10)\n' +
    '    at model.Query._findOne (/Users/<USER>/Desktop/njback/node_modules/mongoose/lib/query.js:2674:8)\n' +
    '    at model.Query.exec (/Users/<USER>/Desktop/njback/node_modules/mongoose/lib/query.js:4604:80)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at SocketController.handleJoinConversation (/Users/<USER>/Desktop/njback/src/socket/socket.controller.ts:58:28)',
  context: {
    socketId: 'R_7_REze4FOwOTqCAAAH',
    userId: '6891106a6280acb97804e1d6',
    conversationId: 'invalid-conversation-id',
    category: 'socket'
  },
  level: 'error',
  message: 'Exception captured',
  timestamp: '2025-08-05 09:06:53'
}
